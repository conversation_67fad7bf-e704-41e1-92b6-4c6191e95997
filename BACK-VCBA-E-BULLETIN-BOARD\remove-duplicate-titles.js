const mysql = require('mysql2/promise');

async function removeDuplicateTitles() {
  console.log('🔍 Finding and removing duplicate holidays by title...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // Find duplicate holidays by title only
    console.log('\n=== Finding duplicate holidays by title ===');
    const [duplicates] = await connection.execute(`
      SELECT 
        title,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id ORDER BY calendar_id) as ids,
        GROUP_CONCAT(event_date ORDER BY calendar_id) as dates,
        GROUP_CONCAT(is_auto_generated ORDER BY calendar_id) as auto_flags
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title
      HAVING COUNT(*) > 1
      ORDER BY count DESC, title
    `);

    if (duplicates.length === 0) {
      console.log('✅ No duplicate holiday titles found');
      await connection.end();
      return;
    }

    console.log(`⚠️ Found ${duplicates.length} holiday titles with duplicates:`);
    duplicates.forEach(dup => {
      const ids = dup.ids.split(',');
      const dates = dup.dates.split(',');
      const autoFlags = dup.auto_flags.split(',');
      
      console.log(`\n"${dup.title}" - ${dup.count} copies:`);
      for (let i = 0; i < ids.length; i++) {
        const autoText = autoFlags[i] === '1' ? 'Auto' : 'Manual';
        console.log(`  - ID: ${ids[i]}, Date: ${dates[i]}, ${autoText}`);
      }
    });

    console.log('\n⚠️ WARNING: This will delete duplicate holidays, keeping only the FIRST occurrence (lowest ID) of each title.');
    console.log('This will help fix the save issues you\'re experiencing.');

    // Calculate total deletions
    let totalToDelete = 0;
    duplicates.forEach(dup => {
      totalToDelete += (dup.count - 1); // Keep 1, delete the rest
    });

    console.log(`\n📊 Summary:`);
    console.log(`- Duplicate title sets: ${duplicates.length}`);
    console.log(`- Total records to delete: ${totalToDelete}`);
    console.log(`- Records to keep: ${duplicates.length}`);

    // Proceed with deletion
    console.log('\n=== Removing duplicates ===');
    
    let totalDeleted = 0;
    
    for (const dup of duplicates) {
      const ids = dup.ids.split(',').map(id => parseInt(id));
      const keepId = Math.min(...ids); // Keep the lowest ID (first occurrence)
      const deleteIds = ids.filter(id => id !== keepId);
      
      console.log(`\nProcessing "${dup.title}":`);
      console.log(`- Keeping ID: ${keepId}`);
      console.log(`- Deleting IDs: ${deleteIds.join(', ')}`);
      
      if (deleteIds.length > 0) {
        const [deleteResult] = await connection.execute(
          `DELETE FROM school_calendar WHERE calendar_id IN (${deleteIds.map(() => '?').join(',')})`,
          deleteIds
        );
        
        console.log(`✅ Deleted ${deleteResult.affectedRows} duplicate records`);
        totalDeleted += deleteResult.affectedRows;
      }
    }

    // Verify the fix
    console.log('\n=== Verifying fix ===');
    const [afterFix] = await connection.execute(`
      SELECT 
        title,
        COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title
      HAVING COUNT(*) > 1
    `);

    if (afterFix.length === 0) {
      console.log('✅ All duplicate titles successfully removed!');
    } else {
      console.log(`⚠️ There are still ${afterFix.length} duplicate titles. May need manual review.`);
      afterFix.forEach(dup => {
        console.log(`- "${dup.title}": ${dup.count} copies`);
      });
    }

    // Final statistics
    console.log('\n=== Final Statistics ===');
    const [finalCount] = await connection.execute(`
      SELECT COUNT(*) as total FROM school_calendar WHERE is_holiday = 1
    `);
    
    const [uniqueTitles] = await connection.execute(`
      SELECT COUNT(DISTINCT title) as unique_titles FROM school_calendar WHERE is_holiday = 1
    `);

    console.log(`Total holidays in database: ${finalCount[0].total}`);
    console.log(`Unique holiday titles: ${uniqueTitles[0].unique_titles}`);
    console.log(`Total duplicates removed: ${totalDeleted}`);

    // Show sample of remaining holidays
    console.log('\n=== Sample remaining holidays ===');
    const [sampleHolidays] = await connection.execute(`
      SELECT calendar_id, title, event_date 
      FROM school_calendar 
      WHERE is_holiday = 1 
      ORDER BY event_date 
      LIMIT 10
    `);

    sampleHolidays.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date})`);
    });

    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Duplicate removal completed!');
    console.log('\n💡 Now try editing holidays again - the save issues should be resolved!');

  } catch (error) {
    console.error('❌ Error removing duplicate titles:', error.message);
  }
}

removeDuplicateTitles();
