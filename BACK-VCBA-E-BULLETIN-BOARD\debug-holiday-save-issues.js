const mysql = require('mysql2/promise');

async function debugHolidaySaveIssues() {
  console.log('🔍 Debugging holiday save issues...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // Check holidays with missing or invalid calendar_id
    console.log('\n=== Checking for holidays with missing calendar_id ===');
    const [missingIds] = await connection.execute(`
      SELECT 
        calendar_id,
        title,
        event_date,
        category_name,
        is_holiday,
        is_auto_generated
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE is_holiday = 1 
        AND (calendar_id IS NULL OR calendar_id = 0)
      ORDER BY event_date
      LIMIT 10
    `);

    if (missingIds.length > 0) {
      console.log(`❌ Found ${missingIds.length} holidays with missing calendar_id:`);
      missingIds.forEach(holiday => {
        console.log(`- ID: ${holiday.calendar_id}, Title: ${holiday.title}, Date: ${holiday.event_date}`);
      });
    } else {
      console.log('✅ All holidays have valid calendar_id');
    }

    // Check for duplicate holidays
    console.log('\n=== Checking for duplicate holidays ===');
    const [duplicates] = await connection.execute(`
      SELECT 
        title,
        event_date,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id) as ids
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title, event_date
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `);

    if (duplicates.length > 0) {
      console.log(`⚠️ Found ${duplicates.length} duplicate holidays:`);
      duplicates.forEach(dup => {
        console.log(`- "${dup.title}" on ${dup.event_date}: ${dup.count} copies (IDs: ${dup.ids})`);
      });
    } else {
      console.log('✅ No duplicate holidays found');
    }

    // Check holidays by category to see if specific categories have issues
    console.log('\n=== Holiday count by category ===');
    const [byCategory] = await connection.execute(`
      SELECT 
        c.name as category_name,
        COUNT(*) as holiday_count,
        MIN(sc.calendar_id) as min_id,
        MAX(sc.calendar_id) as max_id
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE sc.is_holiday = 1
      GROUP BY c.name
      ORDER BY holiday_count DESC
    `);

    byCategory.forEach(cat => {
      console.log(`- ${cat.category_name}: ${cat.holiday_count} holidays (ID range: ${cat.min_id}-${cat.max_id})`);
    });

    // Check for holidays with missing required fields
    console.log('\n=== Checking for holidays with missing required fields ===');
    const [missingFields] = await connection.execute(`
      SELECT 
        calendar_id,
        title,
        event_date,
        category_id,
        description,
        CASE 
          WHEN title IS NULL OR title = '' THEN 'Missing title'
          WHEN event_date IS NULL THEN 'Missing date'
          WHEN category_id IS NULL THEN 'Missing category'
          ELSE 'OK'
        END as issue
      FROM school_calendar 
      WHERE is_holiday = 1 
        AND (title IS NULL OR title = '' OR event_date IS NULL OR category_id IS NULL)
      ORDER BY event_date
      LIMIT 10
    `);

    if (missingFields.length > 0) {
      console.log(`❌ Found ${missingFields.length} holidays with missing required fields:`);
      missingFields.forEach(holiday => {
        console.log(`- ID: ${holiday.calendar_id}, Issue: ${holiday.issue}, Title: "${holiday.title}"`);
      });
    } else {
      console.log('✅ All holidays have required fields');
    }

    // Sample working vs potentially problematic holidays
    console.log('\n=== Sample holidays for comparison ===');
    const [sampleHolidays] = await connection.execute(`
      SELECT 
        calendar_id,
        title,
        event_date,
        category_name,
        description,
        is_auto_generated,
        created_at,
        updated_at
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 5
    `);

    console.log('First 5 holidays in database:');
    sampleHolidays.forEach(holiday => {
      const hasDesc = holiday.description ? 'Has desc' : 'No desc';
      const autoGen = holiday.is_auto_generated ? 'Auto' : 'Manual';
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) [${holiday.category_name}] ${hasDesc}, ${autoGen}`);
    });

    // Check recent updates
    console.log('\n=== Recently updated holidays ===');
    const [recentUpdates] = await connection.execute(`
      SELECT 
        calendar_id,
        title,
        event_date,
        updated_at
      FROM school_calendar 
      WHERE is_holiday = 1 
        AND updated_at > DATE_SUB(NOW(), INTERVAL 1 DAY)
      ORDER BY updated_at DESC
      LIMIT 5
    `);

    if (recentUpdates.length > 0) {
      console.log('Recently updated holidays:');
      recentUpdates.forEach(holiday => {
        console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" updated at ${holiday.updated_at}`);
      });
    } else {
      console.log('No holidays updated in the last 24 hours');
    }

    await connection.end();
    console.log('\n✅ Database connection closed');

    // Recommendations
    console.log('\n' + '='.repeat(60));
    console.log('🔧 RECOMMENDATIONS');
    console.log('='.repeat(60));
    console.log('1. Check browser console for JavaScript errors when saving fails');
    console.log('2. Verify that failing holidays have valid calendar_id values');
    console.log('3. Check if specific holiday categories are causing issues');
    console.log('4. Test with holidays that have different is_auto_generated values');
    console.log('5. Look for network errors in browser dev tools');

  } catch (error) {
    console.error('❌ Error debugging holiday save issues:', error.message);
  }
}

debugHolidaySaveIssues();
