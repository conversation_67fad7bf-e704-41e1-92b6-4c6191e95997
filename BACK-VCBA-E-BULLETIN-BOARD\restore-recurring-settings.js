const mysql = require('mysql2/promise');

async function restoreRecurringSettings() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // RESTORE the correct recurring settings for all holidays
    console.log('🔧 RESTORING recurring settings for all holidays...');
    console.log('Setting is_recurring = 1 and recurrence_pattern = "yearly" for all holidays');
    
    const [updateResult] = await connection.execute(`
      UPDATE school_calendar 
      SET is_recurring = 1, recurrence_pattern = 'yearly' 
      WHERE is_holiday = 1
    `);
    
    console.log(`✅ RESTORED recurring settings for ${updateResult.affectedRows} holiday records`);
    
    // Verify the restoration
    const [afterRestore] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM school_calendar 
      WHERE is_holiday = 1 AND is_recurring = 1 AND recurrence_pattern = 'yearly'
    `);
    
    console.log(`📊 Holidays with correct recurring settings: ${afterRestore[0].count}`);
    
    // Show sample restored holidays
    console.log('\n📋 Sample restored holidays:');
    const [sampleRestored] = await connection.execute(`
      SELECT calendar_id, title, is_recurring, recurrence_pattern, event_date
      FROM school_calendar 
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 5
    `);
    
    sampleRestored.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}", Recurring: ${holiday.is_recurring}, Pattern: ${holiday.recurrence_pattern}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Recurring settings RESTORED correctly!');
    console.log('💡 All holidays now have is_recurring = 1 and recurrence_pattern = yearly as intended');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

restoreRecurringSettings();
