'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _vsop87Bearth = require('astronomia/lib/data/vsop87Bearth.cjs');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _vsop87Bearth__default = /*#__PURE__*/_interopDefaultLegacy(_vsop87Bearth);

const vsop87Bearth = _vsop87Bearth__default["default"];

exports.vsop87Bearth = vsop87Bearth;
