const mysql = require('mysql2/promise');

async function fixHolidayTitles() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Find duplicate titles in holidays
    console.log('\n=== Finding duplicate holiday titles ===');
    const [duplicates] = await connection.execute(`
      SELECT 
        title,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id ORDER BY calendar_id) as ids,
        GROUP_CONCAT(YEAR(event_date) ORDER BY calendar_id) as years
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title
      HAVING COUNT(*) > 1
      ORDER BY count DESC, title
      LIMIT 10
    `);
    
    console.log(`⚠️ Found ${duplicates.length} holiday titles with duplicates:`);
    duplicates.forEach(dup => {
      const ids = dup.ids.split(',');
      const years = dup.years.split(',');
      console.log(`\n"${dup.title}" - ${dup.count} copies:`);
      for (let i = 0; i < Math.min(5, ids.length); i++) {
        console.log(`  - ID: ${ids[i]}, Year: ${years[i]}`);
      }
      if (ids.length > 5) {
        console.log(`  ... and ${ids.length - 5} more`);
      }
    });
    
    // Show total counts
    const [totalHolidays] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [uniqueTitles] = await connection.execute('SELECT COUNT(DISTINCT title) as count FROM school_calendar WHERE is_holiday = 1');
    
    console.log(`\n📊 Statistics:`);
    console.log(`- Total holiday records: ${totalHolidays[0].count}`);
    console.log(`- Unique holiday titles: ${uniqueTitles[0].count}`);
    console.log(`- Average copies per title: ${(totalHolidays[0].count / uniqueTitles[0].count).toFixed(1)}`);
    
    // Option 1: Make titles unique by adding year
    console.log('\n=== SOLUTION OPTIONS ===');
    console.log('Option 1: Add year to titles (e.g., "Christmas Day 2025")');
    console.log('Option 2: Keep one holiday per title (remove duplicates)');
    console.log('Option 3: Keep current structure (holidays work but have duplicate titles)');
    
    console.log('\n⚠️ The current structure actually works fine for the calendar system.');
    console.log('Each holiday has a unique ID and correct date, so editing works properly.');
    console.log('The "duplicate" titles are expected since the same holiday appears each year.');
    
    // Let's check if there are any ACTUAL problematic duplicates (same title AND same year)
    console.log('\n=== Checking for ACTUAL duplicates (same title + same year) ===');
    const [actualDuplicates] = await connection.execute(`
      SELECT 
        title,
        YEAR(event_date) as year,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id) as ids
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title, YEAR(event_date)
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `);
    
    if (actualDuplicates.length > 0) {
      console.log(`❌ Found ${actualDuplicates.length} ACTUAL duplicate problems:`);
      actualDuplicates.forEach(dup => {
        console.log(`- "${dup.title}" in ${dup.year}: ${dup.count} copies (IDs: ${dup.ids})`);
      });
      
      // Fix actual duplicates by keeping only the first one
      console.log('\n🔧 Fixing actual duplicates...');
      let totalFixed = 0;
      
      for (const dup of actualDuplicates) {
        const ids = dup.ids.split(',').map(id => parseInt(id));
        const keepId = Math.min(...ids); // Keep the lowest ID
        const deleteIds = ids.filter(id => id !== keepId);
        
        if (deleteIds.length > 0) {
          const [deleteResult] = await connection.execute(
            `DELETE FROM school_calendar WHERE calendar_id IN (${deleteIds.map(() => '?').join(',')})`,
            deleteIds
          );
          
          console.log(`✅ Fixed "${dup.title}" ${dup.year}: kept ID ${keepId}, deleted ${deleteResult.affectedRows} duplicates`);
          totalFixed += deleteResult.affectedRows;
        }
      }
      
      console.log(`\n🎉 Fixed ${totalFixed} actual duplicate records!`);
    } else {
      console.log('✅ No actual duplicates found! The system is working correctly.');
      console.log('Each holiday title appears once per year, which is the expected behavior.');
    }
    
    // Final verification
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [finalByYear] = await connection.execute(`
      SELECT YEAR(event_date) as year, COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY YEAR(event_date)
      ORDER BY year
      LIMIT 5
    `);
    
    console.log(`\n📊 Final Statistics:`);
    console.log(`- Total holidays: ${finalCount[0].count}`);
    console.log('- Holidays by year (sample):');
    finalByYear.forEach(item => {
      console.log(`  ${item.year}: ${item.count} holidays`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

fixHolidayTitles();
