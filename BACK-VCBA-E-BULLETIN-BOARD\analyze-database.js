const mysql = require('mysql2/promise');

async function removeAndAnalyze() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('=== REMOVING EXISTING HOLIDAYS ===');
    const [result] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${result.affectedRows} holiday records`);
    
    console.log('\n=== DATABASE STRUCTURE ANALYSIS ===');
    
    // 1. Show school_calendar table structure
    console.log('\n1. SCHOOL_CALENDAR TABLE STRUCTURE:');
    const [columns] = await connection.execute('DESCRIBE school_calendar');
    columns.forEach(col => {
      const nullable = col.Null === 'NO' ? '(NOT NULL)' : '(NULL)';
      const key = col.Key ? `[${col.Key}]` : '';
      const defaultVal = col.Default !== null ? `DEFAULT: ${col.Default}` : '';
      console.log(`- ${col.Field}: ${col.Type} ${nullable} ${key} ${defaultVal}`);
    });
    
    // 2. Show foreign key relationships
    console.log('\n2. FOREIGN KEY RELATIONSHIPS:');
    const [fks] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = 'db_ebulletin_system' 
        AND TABLE_NAME = 'school_calendar' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `);
    
    if (fks.length > 0) {
      fks.forEach(fk => {
        console.log(`- ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`);
      });
    } else {
      console.log('- No foreign keys found');
    }
    
    // 3. Show categories table if it exists
    console.log('\n3. CATEGORIES TABLE STRUCTURE:');
    try {
      const [catColumns] = await connection.execute('DESCRIBE categories');
      catColumns.forEach(col => {
        const nullable = col.Null === 'NO' ? '(NOT NULL)' : '(NULL)';
        const key = col.Key ? `[${col.Key}]` : '';
        console.log(`- ${col.Field}: ${col.Type} ${nullable} ${key}`);
      });
      
      // Show existing categories
      console.log('\n4. EXISTING CATEGORIES:');
      const [existingCats] = await connection.execute('SELECT * FROM categories ORDER BY category_id');
      existingCats.forEach(cat => {
        console.log(`- ID: ${cat.category_id}, Name: "${cat.name}", Color: ${cat.color || 'N/A'}`);
      });
    } catch (error) {
      console.log('- Categories table not found or error:', error.message);
    }
    
    // 4. Show sample school_calendar records
    console.log('\n5. SAMPLE SCHOOL_CALENDAR RECORDS:');
    const [samples] = await connection.execute('SELECT * FROM school_calendar LIMIT 5');
    samples.forEach(record => {
      console.log(`- ID: ${record.calendar_id}, Title: "${record.title}", Date: ${record.event_date}, Holiday: ${record.is_holiday}, Recurring: ${record.is_recurring}`);
    });
    
    // 5. Check for recurring patterns
    console.log('\n6. RECURRING PATTERN ANALYSIS:');
    const [recurringInfo] = await connection.execute(`
      SELECT 
        is_recurring,
        recurrence_pattern,
        COUNT(*) as count
      FROM school_calendar 
      GROUP BY is_recurring, recurrence_pattern
    `);
    
    recurringInfo.forEach(info => {
      console.log(`- Recurring: ${info.is_recurring}, Pattern: ${info.recurrence_pattern || 'NULL'}, Count: ${info.count}`);
    });
    
    await connection.end();
    console.log('\n=== ANALYSIS COMPLETE ===');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeAndAnalyze();
