{"name": "caldate", "version": "2.0.5", "description": "Calendar date for date-holidays", "keywords": ["calendar", "date"], "homepage": "https://github.com/commenthol/caldate", "bugs": {"url": "https://github.com/commenthol/caldate/issues"}, "repository": {"type": "git", "url": "git+https://github.com/commenthol/caldate.git"}, "license": "ISC", "author": "commenthol <<EMAIL>>", "maintainers": ["commenthol <<EMAIL>>"], "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./lib/index.cjs"}, "./package.json": "./package.json"}, "main": "./lib/index.cjs", "module": "./src/index.js", "types": "./types", "directories": {"lib": "lib", "test": "test"}, "scripts": {"all": "npm-run-all clean lint build test", "ci": "npm run all", "build": "rollup -c", "clean": "rimraf lib es coverage .nyc_output", "clean:all": "npm-run-all clean clean:node_modules", "clean:node_modules": "rimraf node_modules", "coverage": "c8 -r text -r html npm run test:ci", "lint": "eslint --ext .js .", "prepublishOnly": "npm run all", "readme": "markedpp --githubid -i README.md -o README.md", "test": "npm-run-all test:ci test:ts", "test:ci": "mocha", "test:ts": "dtslint types"}, "mocha": {"checkLeaks": true, "colors": true}, "dependencies": {"moment-timezone": "^0.5.43"}, "devDependencies": {"c8": "^7.13.0", "dtslint": "^4.2.1", "eslint": "^8.40.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "mocha": "^10.2.0", "npm-run-all": "^4.1.5", "rimraf": "^5.0.0", "rollup": "^3.21.6", "typescript": "^5.0.4"}, "engines": {"node": ">=12.0.0"}}