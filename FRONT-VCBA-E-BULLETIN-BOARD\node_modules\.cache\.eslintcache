[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "68", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx": "69", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts": "70"}, {"size": 554, "mtime": 1752306944000, "results": "71", "hashOfConfig": "72"}, {"size": 419, "mtime": 1751128028000, "results": "73", "hashOfConfig": "72"}, {"size": 6159, "mtime": 1753071175461, "results": "74", "hashOfConfig": "72"}, {"size": 7830, "mtime": 1752857570939, "results": "75", "hashOfConfig": "72"}, {"size": 3769, "mtime": 1752721564000, "results": "76", "hashOfConfig": "72"}, {"size": 6136, "mtime": 1752857570952, "results": "77", "hashOfConfig": "72"}, {"size": 7447, "mtime": 1752271830000, "results": "78", "hashOfConfig": "72"}, {"size": 15866, "mtime": 1752865554796, "results": "79", "hashOfConfig": "72"}, {"size": 48728, "mtime": 1753079237325, "results": "80", "hashOfConfig": "72"}, {"size": 45894, "mtime": 1752867480062, "results": "81", "hashOfConfig": "72"}, {"size": 15127, "mtime": 1752863733791, "results": "82", "hashOfConfig": "72"}, {"size": 5757, "mtime": 1752391390000, "results": "83", "hashOfConfig": "72"}, {"size": 90955, "mtime": 1753079468887, "results": "84", "hashOfConfig": "72"}, {"size": 5269, "mtime": 1751481708000, "results": "85", "hashOfConfig": "72"}, {"size": 62476, "mtime": 1752106868000, "results": "86", "hashOfConfig": "72"}, {"size": 85684, "mtime": 1753079484562, "results": "87", "hashOfConfig": "72"}, {"size": 56, "mtime": 1751129202000, "results": "88", "hashOfConfig": "72"}, {"size": 1342, "mtime": 1751155290000, "results": "89", "hashOfConfig": "72"}, {"size": 1688, "mtime": 1751208620000, "results": "90", "hashOfConfig": "72"}, {"size": 103, "mtime": 1751140878000, "results": "91", "hashOfConfig": "72"}, {"size": 232, "mtime": 1751541102000, "results": "92", "hashOfConfig": "72"}, {"size": 3996, "mtime": 1751807708000, "results": "93", "hashOfConfig": "72"}, {"size": 8102, "mtime": 1752863733789, "results": "94", "hashOfConfig": "72"}, {"size": 10798, "mtime": 1752863733876, "results": "95", "hashOfConfig": "72"}, {"size": 17132, "mtime": 1752721564000, "results": "96", "hashOfConfig": "72"}, {"size": 6186, "mtime": 1751790574000, "results": "97", "hashOfConfig": "72"}, {"size": 14284, "mtime": 1753079932241, "results": "98", "hashOfConfig": "72"}, {"size": 8587, "mtime": 1752337674000, "results": "99", "hashOfConfig": "72"}, {"size": 23306, "mtime": 1752878817658, "results": "100", "hashOfConfig": "72"}, {"size": 12906, "mtime": 1752333634000, "results": "101", "hashOfConfig": "72"}, {"size": 5715, "mtime": 1752872506322, "results": "102", "hashOfConfig": "72"}, {"size": 9834, "mtime": 1753077700688, "results": "103", "hashOfConfig": "72"}, {"size": 8214, "mtime": 1752334762000, "results": "104", "hashOfConfig": "72"}, {"size": 12982, "mtime": 1752333550000, "results": "105", "hashOfConfig": "72"}, {"size": 16951, "mtime": 1752879455591, "results": "106", "hashOfConfig": "72"}, {"size": 15593, "mtime": 1752760674000, "results": "107", "hashOfConfig": "72"}, {"size": 12325, "mtime": 1752330204000, "results": "108", "hashOfConfig": "72"}, {"size": 28178, "mtime": 1753079518460, "results": "109", "hashOfConfig": "72"}, {"size": 24168, "mtime": 1752867418410, "results": "110", "hashOfConfig": "72"}, {"size": 36840, "mtime": 1753092606934, "results": "111", "hashOfConfig": "72"}, {"size": 6146, "mtime": 1753071175460, "results": "112", "hashOfConfig": "72"}, {"size": 9288, "mtime": 1752863733792, "results": "113", "hashOfConfig": "72"}, {"size": 11500, "mtime": 1753071175461, "results": "114", "hashOfConfig": "72"}, {"size": 5908, "mtime": 1752330316000, "results": "115", "hashOfConfig": "72"}, {"size": 2063, "mtime": 1751140856000, "results": "116", "hashOfConfig": "72"}, {"size": 2236, "mtime": 1751374982000, "results": "117", "hashOfConfig": "72"}, {"size": 4237, "mtime": 1751374890000, "results": "118", "hashOfConfig": "72"}, {"size": 230, "mtime": 1751371668000, "results": "119", "hashOfConfig": "72"}, {"size": 10813, "mtime": 1752761372000, "results": "120", "hashOfConfig": "72"}, {"size": 14506, "mtime": 1752879590759, "results": "121", "hashOfConfig": "72"}, {"size": 26448, "mtime": 1752380598000, "results": "122", "hashOfConfig": "72"}, {"size": 10147, "mtime": 1752334796000, "results": "123", "hashOfConfig": "72"}, {"size": 10510, "mtime": 1752310980000, "results": "124", "hashOfConfig": "72"}, {"size": 10877, "mtime": 1752092600000, "results": "125", "hashOfConfig": "72"}, {"size": 7318, "mtime": 1752381124000, "results": "126", "hashOfConfig": "72"}, {"size": 5263, "mtime": 1752867135753, "results": "127", "hashOfConfig": "72"}, {"size": 19520, "mtime": 1752338090000, "results": "128", "hashOfConfig": "72"}, {"size": 13444, "mtime": 1752870507174, "results": "129", "hashOfConfig": "72"}, {"size": 16870, "mtime": 1752338106000, "results": "130", "hashOfConfig": "72"}, {"size": 616, "mtime": 1752865556056, "results": "131", "hashOfConfig": "72"}, {"size": 15760, "mtime": 1752828048867, "results": "132", "hashOfConfig": "72"}, {"size": 9958, "mtime": 1751375132000, "results": "133", "hashOfConfig": "72"}, {"size": 42, "mtime": 1751129052000, "results": "134", "hashOfConfig": "72"}, {"size": 20869, "mtime": 1752885135797, "results": "135", "hashOfConfig": "72"}, {"size": 10034, "mtime": 1752860879212, "results": "136", "hashOfConfig": "72"}, {"size": 9297, "mtime": 1751476824000, "results": "137", "hashOfConfig": "72"}, {"size": 3321, "mtime": 1752876410067, "results": "138", "hashOfConfig": "72"}, {"size": 3686, "mtime": 1752890875441, "results": "139", "hashOfConfig": "72"}, {"size": 18735, "mtime": 1753077009362, "results": "140", "hashOfConfig": "72"}, {"size": 7423, "mtime": 1753077217399, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["352"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["353"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["354", "355", "356", "357"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["358", "359", "360"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["361", "362", "363", "364", "365", "366"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["367", "368"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx", ["369", "370", "371", "372", "373", "374", "375"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["376"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["377"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["378"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["379"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["380"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["381", "382", "383"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["384"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["385"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["386", "387", "388", "389", "390"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["391", "392", "393", "394"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["395"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["396", "397", "398", "399"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["400", "401", "402", "403", "404"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["405", "406", "407", "408", "409"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["410", "411", "412", "413", "414", "415"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["416", "417", "418"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["419"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["420", "421"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["422"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["423", "424", "425"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["426", "427", "428"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["429"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["430", "431", "432"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx", ["433", "434", "435"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts", ["436"], [], {"ruleId": "437", "severity": 1, "message": "438", "line": 3, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 3, "endColumn": 22}, {"ruleId": "437", "severity": 1, "message": "441", "line": 2, "column": 28, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 38}, {"ruleId": "437", "severity": 1, "message": "442", "line": 6, "column": 132, "nodeType": "439", "messageId": "440", "endLine": 6, "endColumn": 137}, {"ruleId": "443", "severity": 1, "message": "444", "line": 217, "column": 6, "nodeType": "445", "endLine": 217, "endColumn": 57, "suggestions": "446"}, {"ruleId": "443", "severity": 1, "message": "447", "line": 217, "column": 7, "nodeType": "448", "endLine": 217, "endColumn": 32}, {"ruleId": "443", "severity": 1, "message": "447", "line": 217, "column": 34, "nodeType": "448", "endLine": 217, "endColumn": 56}, {"ruleId": "437", "severity": 1, "message": "449", "line": 53, "column": 19, "nodeType": "439", "messageId": "440", "endLine": 53, "endColumn": 29}, {"ruleId": "437", "severity": 1, "message": "450", "line": 83, "column": 5, "nodeType": "439", "messageId": "440", "endLine": 83, "endColumn": 10}, {"ruleId": "443", "severity": 1, "message": "451", "line": 162, "column": 6, "nodeType": "445", "endLine": 162, "endColumn": 82, "suggestions": "452"}, {"ruleId": "437", "severity": 1, "message": "453", "line": 3, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 3, "endColumn": 29}, {"ruleId": "437", "severity": 1, "message": "454", "line": 23, "column": 3, "nodeType": "439", "messageId": "440", "endLine": 23, "endColumn": 7}, {"ruleId": "443", "severity": 1, "message": "455", "line": 101, "column": 6, "nodeType": "445", "endLine": 101, "endColumn": 17, "suggestions": "456"}, {"ruleId": "437", "severity": 1, "message": "457", "line": 473, "column": 31, "nodeType": "439", "messageId": "440", "endLine": 473, "endColumn": 45}, {"ruleId": "437", "severity": 1, "message": "458", "line": 536, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 536, "endColumn": 28}, {"ruleId": "437", "severity": 1, "message": "459", "line": 702, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 702, "endColumn": 24}, {"ruleId": "437", "severity": 1, "message": "460", "line": 2, "column": 57, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 73}, {"ruleId": "437", "severity": 1, "message": "461", "line": 17, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 17, "endColumn": 23}, {"ruleId": "437", "severity": 1, "message": "453", "line": 6, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 6, "endColumn": 29}, {"ruleId": "437", "severity": 1, "message": "462", "line": 6, "column": 31, "nodeType": "439", "messageId": "440", "endLine": 6, "endColumn": 46}, {"ruleId": "437", "severity": 1, "message": "463", "line": 11, "column": 15, "nodeType": "439", "messageId": "440", "endLine": 11, "endColumn": 27}, {"ruleId": "443", "severity": 1, "message": "455", "line": 100, "column": 6, "nodeType": "445", "endLine": 100, "endColumn": 17, "suggestions": "464"}, {"ruleId": "437", "severity": 1, "message": "457", "line": 391, "column": 31, "nodeType": "439", "messageId": "440", "endLine": 391, "endColumn": 45}, {"ruleId": "437", "severity": 1, "message": "465", "line": 491, "column": 14, "nodeType": "439", "messageId": "440", "endLine": 491, "endColumn": 34}, {"ruleId": "437", "severity": 1, "message": "459", "line": 721, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 721, "endColumn": 24}, {"ruleId": "443", "severity": 1, "message": "466", "line": 39, "column": 6, "nodeType": "445", "endLine": 39, "endColumn": 16, "suggestions": "467"}, {"ruleId": "437", "severity": 1, "message": "468", "line": 35, "column": 32, "nodeType": "439", "messageId": "440", "endLine": 35, "endColumn": 33}, {"ruleId": "437", "severity": 1, "message": "469", "line": 3, "column": 25, "nodeType": "439", "messageId": "440", "endLine": 3, "endColumn": 37}, {"ruleId": "443", "severity": 1, "message": "455", "line": 64, "column": 6, "nodeType": "445", "endLine": 64, "endColumn": 17, "suggestions": "470"}, {"ruleId": "437", "severity": 1, "message": "471", "line": 93, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 93, "endColumn": 19}, {"ruleId": "437", "severity": 1, "message": "472", "line": 7, "column": 3, "nodeType": "439", "messageId": "440", "endLine": 7, "endColumn": 15}, {"ruleId": "443", "severity": 1, "message": "473", "line": 82, "column": 6, "nodeType": "445", "endLine": 82, "endColumn": 33, "suggestions": "474"}, {"ruleId": "443", "severity": 1, "message": "475", "line": 82, "column": 7, "nodeType": "448", "endLine": 82, "endColumn": 32}, {"ruleId": "443", "severity": 1, "message": "476", "line": 57, "column": 6, "nodeType": "445", "endLine": 57, "endColumn": 16, "suggestions": "477"}, {"ruleId": "437", "severity": 1, "message": "471", "line": 93, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 93, "endColumn": 19}, {"ruleId": "437", "severity": 1, "message": "478", "line": 2, "column": 31, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 55}, {"ruleId": "437", "severity": 1, "message": "479", "line": 8, "column": 15, "nodeType": "439", "messageId": "440", "endLine": 8, "endColumn": 26}, {"ruleId": "443", "severity": 1, "message": "480", "line": 121, "column": 6, "nodeType": "445", "endLine": 121, "endColumn": 105, "suggestions": "481"}, {"ruleId": "443", "severity": 1, "message": "475", "line": 121, "column": 7, "nodeType": "448", "endLine": 121, "endColumn": 30}, {"ruleId": "443", "severity": 1, "message": "482", "line": 324, "column": 6, "nodeType": "445", "endLine": 324, "endColumn": 48, "suggestions": "483"}, {"ruleId": "437", "severity": 1, "message": "484", "line": 3, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 3, "endColumn": 21}, {"ruleId": "443", "severity": 1, "message": "455", "line": 56, "column": 6, "nodeType": "445", "endLine": 56, "endColumn": 17, "suggestions": "485"}, {"ruleId": "437", "severity": 1, "message": "486", "line": 147, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 147, "endColumn": 21}, {"ruleId": "443", "severity": 1, "message": "487", "line": 180, "column": 6, "nodeType": "445", "endLine": 180, "endColumn": 23, "suggestions": "488"}, {"ruleId": "443", "severity": 1, "message": "489", "line": 182, "column": 6, "nodeType": "445", "endLine": 182, "endColumn": 113, "suggestions": "490"}, {"ruleId": "437", "severity": 1, "message": "491", "line": 2, "column": 34, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 37}, {"ruleId": "443", "severity": 1, "message": "455", "line": 65, "column": 6, "nodeType": "445", "endLine": 65, "endColumn": 17, "suggestions": "492"}, {"ruleId": "493", "severity": 1, "message": "494", "line": 248, "column": 11, "nodeType": "495", "endLine": 262, "endColumn": 13}, {"ruleId": "493", "severity": 1, "message": "494", "line": 332, "column": 19, "nodeType": "495", "endLine": 346, "endColumn": 21}, {"ruleId": "437", "severity": 1, "message": "496", "line": 2, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 24}, {"ruleId": "437", "severity": 1, "message": "497", "line": 12, "column": 3, "nodeType": "439", "messageId": "440", "endLine": 12, "endColumn": 17}, {"ruleId": "437", "severity": 1, "message": "498", "line": 13, "column": 3, "nodeType": "439", "messageId": "440", "endLine": 13, "endColumn": 28}, {"ruleId": "443", "severity": 1, "message": "499", "line": 140, "column": 6, "nodeType": "445", "endLine": 140, "endColumn": 57, "suggestions": "500"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 426, "column": 44, "nodeType": "503", "messageId": "504", "endLine": 426, "endColumn": 98}, {"ruleId": "437", "severity": 1, "message": "505", "line": 1, "column": 22, "nodeType": "439", "messageId": "440", "endLine": 1, "endColumn": 37}, {"ruleId": "437", "severity": 1, "message": "506", "line": 1, "column": 39, "nodeType": "439", "messageId": "440", "endLine": 1, "endColumn": 56}, {"ruleId": "437", "severity": 1, "message": "507", "line": 2, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 26}, {"ruleId": "508", "severity": 1, "message": "509", "line": 581, "column": 3, "nodeType": "510", "messageId": "511", "endLine": 583, "endColumn": 4}, {"ruleId": "508", "severity": 1, "message": "509", "line": 647, "column": 3, "nodeType": "510", "messageId": "511", "endLine": 649, "endColumn": 4}, {"ruleId": "443", "severity": 1, "message": "512", "line": 128, "column": 6, "nodeType": "445", "endLine": 128, "endColumn": 18, "suggestions": "513"}, {"ruleId": "443", "severity": 1, "message": "514", "line": 173, "column": 6, "nodeType": "445", "endLine": 173, "endColumn": 33, "suggestions": "515"}, {"ruleId": "443", "severity": 1, "message": "514", "line": 204, "column": 6, "nodeType": "445", "endLine": 204, "endColumn": 21, "suggestions": "516"}, {"ruleId": "443", "severity": 1, "message": "514", "line": 239, "column": 6, "nodeType": "445", "endLine": 239, "endColumn": 33, "suggestions": "517"}, {"ruleId": "443", "severity": 1, "message": "518", "line": 299, "column": 6, "nodeType": "445", "endLine": 299, "endColumn": 22, "suggestions": "519"}, {"ruleId": "443", "severity": 1, "message": "520", "line": 306, "column": 6, "nodeType": "445", "endLine": 306, "endColumn": 18, "suggestions": "521"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 35, "column": 44, "nodeType": "503", "messageId": "504", "endLine": 35, "endColumn": 98}, {"ruleId": "501", "severity": 1, "message": "522", "line": 96, "column": 46, "nodeType": "503", "messageId": "504", "endLine": 96, "endColumn": 97}, {"ruleId": "523", "severity": 1, "message": "524", "line": 234, "column": 1, "nodeType": "525", "endLine": 248, "endColumn": 3}, {"ruleId": "437", "severity": 1, "message": "526", "line": 23, "column": 11, "nodeType": "439", "messageId": "440", "endLine": 23, "endColumn": 28}, {"ruleId": "443", "severity": 1, "message": "455", "line": 100, "column": 6, "nodeType": "445", "endLine": 100, "endColumn": 17, "suggestions": "527"}, {"ruleId": "443", "severity": 1, "message": "528", "line": 375, "column": 6, "nodeType": "445", "endLine": 375, "endColumn": 8, "suggestions": "529"}, {"ruleId": "530", "severity": 1, "message": "531", "line": 259, "column": 7, "nodeType": "495", "endLine": 273, "endColumn": 8}, {"ruleId": "443", "severity": 1, "message": "455", "line": 66, "column": 6, "nodeType": "445", "endLine": 66, "endColumn": 17, "suggestions": "532"}, {"ruleId": "437", "severity": 1, "message": "533", "line": 385, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 385, "endColumn": 24}, {"ruleId": "443", "severity": 1, "message": "534", "line": 404, "column": 6, "nodeType": "445", "endLine": 404, "endColumn": 8, "suggestions": "535"}, {"ruleId": "437", "severity": 1, "message": "536", "line": 2, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 14}, {"ruleId": "437", "severity": 1, "message": "537", "line": 90, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 90, "endColumn": 33}, {"ruleId": "437", "severity": 1, "message": "538", "line": 94, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 94, "endColumn": 26}, {"ruleId": "437", "severity": 1, "message": "539", "line": 4, "column": 27, "nodeType": "439", "messageId": "440", "endLine": 4, "endColumn": 39}, {"ruleId": "437", "severity": 1, "message": "536", "line": 2, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 14}, {"ruleId": "437", "severity": 1, "message": "537", "line": 105, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 105, "endColumn": 33}, {"ruleId": "437", "severity": 1, "message": "540", "line": 110, "column": 9, "nodeType": "439", "messageId": "440", "endLine": 110, "endColumn": 16}, {"ruleId": "437", "severity": 1, "message": "541", "line": 2, "column": 49, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 60}, {"ruleId": "437", "severity": 1, "message": "491", "line": 3, "column": 57, "nodeType": "439", "messageId": "440", "endLine": 3, "endColumn": 60}, {"ruleId": "443", "severity": 1, "message": "542", "line": 40, "column": 6, "nodeType": "445", "endLine": 40, "endColumn": 19, "suggestions": "543"}, {"ruleId": "437", "severity": 1, "message": "544", "line": 2, "column": 10, "nodeType": "439", "messageId": "440", "endLine": 2, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'ToastProps' is defined but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["545"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["546"], "'announcementService' is defined but never used.", "'Edit' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["547"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "'calendarService' is defined but never used.", "'Announcement' is defined but never used.", ["548"], "'refreshAnnouncements' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["549"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", ["550"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useCallback has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", ["551"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["552"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["553"], "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["554"], "'getImageUrl' is defined but never used.", ["555"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["556"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["557"], "'Eye' is defined but never used.", ["558"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["559"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["560"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["561"], ["562"], ["563"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["564"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["565"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["566"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["567"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["568"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["569"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", "'SyncResults' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["570"], "'ApiResponse' is defined but never used.", {"desc": "571", "fix": "572"}, {"desc": "573", "fix": "574"}, {"desc": "575", "fix": "576"}, {"desc": "575", "fix": "577"}, {"desc": "578", "fix": "579"}, {"desc": "575", "fix": "580"}, {"desc": "571", "fix": "581"}, {"desc": "582", "fix": "583"}, {"desc": "584", "fix": "585"}, {"desc": "586", "fix": "587"}, {"desc": "575", "fix": "588"}, {"desc": "589", "fix": "590"}, {"desc": "591", "fix": "592"}, {"desc": "575", "fix": "593"}, {"desc": "594", "fix": "595"}, {"desc": "596", "fix": "597"}, {"desc": "598", "fix": "599"}, {"desc": "600", "fix": "601"}, {"desc": "598", "fix": "602"}, {"desc": "603", "fix": "604"}, {"desc": "605", "fix": "606"}, {"desc": "575", "fix": "607"}, {"desc": "608", "fix": "609"}, {"desc": "575", "fix": "610"}, {"desc": "608", "fix": "611"}, {"desc": "612", "fix": "613"}, "Update the dependencies array to be: [currentDate]", {"range": "614", "text": "615"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "616", "text": "617"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "618", "text": "619"}, {"range": "620", "text": "619"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "621", "text": "622"}, {"range": "623", "text": "619"}, {"range": "624", "text": "615"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "625", "text": "626"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "627", "text": "628"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "629", "text": "630"}, {"range": "631", "text": "619"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "632", "text": "633"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "634", "text": "635"}, {"range": "636", "text": "619"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "637", "text": "638"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "639", "text": "640"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "641", "text": "642"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "643", "text": "644"}, {"range": "645", "text": "642"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "646", "text": "647"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "648", "text": "649"}, {"range": "650", "text": "619"}, "Update the dependencies array to be: [images]", {"range": "651", "text": "652"}, {"range": "653", "text": "619"}, {"range": "654", "text": "652"}, "Update the dependencies array to be: [currentYear, loadData]", {"range": "655", "text": "656"}, [7993, 8044], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [3031, 3042], "[imagePath, imageUrl]", [2967, 2978], [900, 910], "[duration, handleClose]", [1735, 1746], [3099, 3126], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636], [1557, 1570], "[currentYear, loadData]"]