holidays:
  # @source http://www.datumsrechner.de/FeiertageSchweiz.pdf
  # @source https://www.ch.ch/de/ferien-und-feiertage/
  # @source https://www.ejpd.admin.ch/dam/data/bj/publiservice/service/zivilprozessrecht/kant-feiertage.pdf
  # @attrib https://fr.wikipedia.org/wiki/Jours_f%C3%A9ri%C3%A9s_en_Suisse
  # @attrib https://de.m.wikipedia.org/wiki/Feiertage_in_der_Schweiz
  # @source https://fr.wikipedia.org/wiki/Berchtoldstag
  CH:
    names:
      de: Schweiz
      fr: Suisse
      it: Svizzera
      en: Switzerland
    dayoff: sunday
    langs:
      - de-ch
      - de
      - fr
      - it
    zones:
      - Europe/Zurich
    days:
      01-01:
        _name: 01-01
      easter -3:
        _name: easter -3
        type: observance
      easter -2:
        _name: easter -2
      easter:
        _name: easter
      easter 1:
        _name: easter 1
      easter 39:
        _name: easter 39
        name:
          de: Auffahrt
      easter 49:
        _name: easter 49
      easter 50:
        _name: easter 50
      2nd sunday in May:
        _name: Mothers Day
        type: observance
      1st sunday in June:
        name:
          de: Vätertag
          fr: Fête des pères
        type: observance
        active:
          - from: "2009-01-01"
      08-01:
        name:
          de: Bundesfeiertag
          fr: Fête nationale
          it: Giorno festivo federale
      3rd sunday after 09-01:
        name:
          de: Eidg. Dank-, Buss- und Bettag
          fr: Jeûne fédéral
          it: Digiuno federale
          en: Federal Day of Thanksgiving, Repentance and Prayer
      12-25:
        _name: 12-25
        name:
          de: Weihnachtstag
      12-26:
        _name: 12-26
        name:
          de: Stephanstag
          fr: Saint-Etienne
    states:
      ZH:
        names:
          de: Kanton Zürich
          fr: Canton de Zurich
          it: Canton Zurigo
          en: Canton of Zürich
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          05-01:
            _name: 05-01
          easter 50:
            _name: easter 50
          monday after 2nd saturday in September 13:00:
            name:
              de: Knabenschiessen
            type: optional
      BE:
        names:
          de: Kanton Bern
          fr: Canton de Berne
          it: Canton Berna
          en: Canton of Bern
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: 2 janvier
      LU:
        names:
          de: Kanton Luzern
          fr: Canton de Lucerne
          it: Canton Lucerna
          en: Canton of Lucerne
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          03-19:
            _name: 03-19
            type: observance
          easter 1:
            _name: easter 1
            type: optional
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
      UR:
        names:
          de: Kanton Uri
          fr: Canton d'Uri
          it: Canton Uri
          en: Canton of Uri
        days:
          01-06:
            _name: 01-06
          03-19:
            _name: 03-19
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
          12-26: false
          12-26 not on monday, friday:
            _name: 12-26
      SZ:
        names:
          de: Kanton Schwyz
          fr: Canton de Schwytz
          it: Canton Svitto
          en: Canton of Schwyz
        days:
          01-06:
            _name: 01-06
          03-19:
            _name: 03-19
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
      OW:
        names:
          de: Kanton Obwalden
          fr: Canton d'Obwald
          it: Canton Obvaldo
          en: Canton of Obwalden
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          easter 1:
            _name: easter 1
            type: optional
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          09-25:
            name:
              de: Bruderklausenfest
              fr: Saint-Nicholas-de-Flüe
              en: Saint Nicholas of Flüe
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
          12-26:
            _name: 12-26
            type: optional
      NW:
        names:
          de: Kanton Nidwalden
          fr: Canton de Nidwald
          it: Canton Nidvaldo
          en: Canton of Nidwalden
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: bank
          03-19:
            _name: 03-19
          easter 1:
            _name: easter 1
            type: optional
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
          12-26:
            _name: 12-26
            type: optional
      GL:
        sources:
          - https://www.gl.ch/verwaltung/staatskanzlei/oeffentliche-feiertage.html/1335
        names:
          de: Kanton Glarus
          fr: Canton de Glaris
          it: Canton Glarona
          en: Canton of Glarus
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          # if falls on easter -3 (Maundy Thursday) then move to next Thursday
          Thursday after 04-02 if is observance holiday then next Thursday:
            name:
              de: Näfelser Fahrt
              fr: Bataille de Näfels
          3rd sunday after 09-01: false
          11-01:
            _name: 11-01
          12-24:
            _name: 12-24
          12-31:
            _name: 12-31
      ZG:
        names:
          de: Kanton Zug
          fr: Canton de Zoug
          it: Canton Zugo
          en: Canton of Zug
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          easter 1:
            _name: easter 1
            type: optional
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
          12-26:
            _name: 12-26
            type: optional
      FR:
        names:
          fr: Canton de Fribourg
          de: Kanton Freiburg
          it: Canton Friburgo
          en: Canton of Fribourg
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          easter 1:
            _name: easter 1
            type: optional
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
            note: >
              excluding communities: Agriswil, Altavilla, Büchslen, Cordast, Courgevaux, Courlevon, Fräschels, Galmiz, Gempenach, Greng, Jeuss, Kerzers, Lurtigen, Meyriez, Muntelier, Murten, Ried bei Kerzers (halb), Salvenach, Ulmiz, Bas-Vully, Haut-Vully
          08-15:
            _name: 08-15
            note: >
              excluding communities: Agriswil, Altavilla, Büchslen, Cordast, Courgevaux, Courlevon, Fräschels, Galmiz, Gempenach, Greng, Jeuss, Kerzers, Lurtigen, Meyriez, Muntelier, Murten, Ried bei Kerzers (halb), Salvenach, Ulmiz, Bas-Vully, Haut-Vully
          11-01:
            _name: 11-01
            note: >
              excluding communities: Agriswil, Altavilla, Büchslen, Cordast, Courgevaux, Courlevon, Fräschels, Galmiz, Gempenach, Greng, Jeuss, Kerzers, Lurtigen, Meyriez, Muntelier, Murten, Ried bei Kerzers (halb), Salvenach, Ulmiz, Bas-Vully, Haut-Vully
          12-08:
            _name: 12-08
            note: >
              excluding communities: Agriswil, Altavilla, Büchslen, Cordast, Courgevaux, Courlevon, Fräschels, Galmiz, Gempenach, Greng, Jeuss, Kerzers, Lurtigen, Meyriez, Muntelier, Murten, Ried bei Kerzers (halb), Salvenach, Ulmiz, Bas-Vully, Haut-Vully
          12-26:
            _name: 12-26
            type: optional
      SO:
        names:
          de: Kanton Solothurn
          fr: Canton de Soleure
          it: Canton Soletta
          en: Canton of Solothurn
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          03-19:
            _name: 03-19
            note: >
              Only in communities: Fulenbach, Walterswil, Wisen, Metzerlen, Nulgar-St. Pantaleon, Rodersdorf, Bärschwil, Büsserach
          easter 1:
            _name: easter 1
            type: optional
          05-01 12:00:
            _name: 05-01
          easter 50:
            _name: easter 50
            type: optional
          easter 60:
            _name: easter 60
            note: is optional in Bucheggberg
          08-15:
            _name: 08-15
            note: is optional in Bucheggberg
          11-01:
            _name: 11-01
            note: is optional in Bucheggberg
          12-26:
            _name: 12-26
            type: optional
      BS:
        names:
          de: Kanton Basel-Stadt
          fr: Canton de Bâle-Ville
          it: Canton Basilea Città
          en: Canton of Basel-City
        days:
          05-01:
            _name: 05-01
      BL:
        names:
          de: Kanton Basel-Landschaft
          fr: Canton de Bâle-Campagne
          it: Canton Basilea Campagna
          en: Canton of Basel-Country
        days:
          05-01:
            _name: 05-01
          easter 60:
            _name: easter 60
            type: observance
      SH:
        names:
          de: Kanton Schaffhausen
          fr: Canton de Schaffhouse
          it: Canton Sciaffusa
          en: Canton of Schaffhausen
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: optional
          05-01:
            _name: 05-01
      AR:
        names:
          de: Kanton Appenzell Ausserrhoden
          fr: Canton d'Appenzell Rhodes-Extérieures
          it: Canton Appenzello Esterno
          en: Canton of Appenzell Outer Rhodes
        days:
          12-26: false
          12-26 not on monday:
            _name: 12-26
      AI:
        names:
          de: Kanton Appenzell Innerrhoden
          fr: Canton d'Appenzell Rhodes-Intérieures
          it: Canton Appenzello Interno
          en: Canton of Appenzell Inner-Rhodes
        days:
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          09-22:
            name:
              de: Mauritiustag
            note: >
              excluding: Bezirk Oberegg
          11-01:
            _name: 11-01
          12-26:
            type: observance
          12-26 not on tuesday, saturday:
            _name: 12-26
      SG:
        names:
          de: Kanton St. Gallen
          fr: Canton de Saint-Gall
          it: Canton San Gallo
          en: Canton of St. Gallen
        days:
          11-01:
            _name: 11-01
      GR:
        names:
          de: Kanton Graubünden
          it: Cantone dei Grigioni
          fr: Canton des Grisons
          en: Canton of Grisons
        langs:
          - de-ch
          - de
          - it
          - fr
        days:
          01-06:
            _name: 01-06
            type: observance
          03-19:
            _name: 03-19
            type: observance
          easter -2:
            _name: easter -2
            type: optional
          easter 60:
            _name: easter 60
            type: observance
          08-15:
            _name: 08-15
            type: observance
          11-01:
            _name: 11-01
            type: observance
          12-08:
            _name: 12-08
            type: observance
      AG:
        names:
          de: Kanton Aargau
          fr: Canton d'Argovie
          it: Canton Argovia
          en: Canton of Aargau
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
          05-01:
            _name: 05-01
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
      TG:
        names:
          de: Kanton Thurgau
          fr: Canton de Thurgovie
          it: Canton Turgovia
          en: Canton of Thurgau
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
          05-01:
            _name: 05-01
      TI:
        names:
          it: Canton Ticino
          de: Kanton Tessin
          fr: Canton du Tessin
          en: Canton of Ticino
        langs:
          - it
          - de-ch
          - de
          - fr
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: bank
          01-06:
            _name: 01-06
            name:
              it: Epifania
          03-19:
            _name: 03-19
          easter -2: false
          easter 60:
            _name: easter 60
          05-01:
            _name: 05-01
          06-29:
            _name: 06-29
          # Vätertag is observed on 03-19
          1st sunday in June: false
          08-15:
            _name: 08-15
            name:
              it: Assunzione
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
      VD:
        names:
          fr: Canton de Vaud
          de: Kanton Waadt
          it: Canton Vaud
          en: Canton of Vaud
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: 2 Janvier
          monday after 3rd sunday in September:
            name:
              de: Bettagsmontag
              fr: Lundi du Jeûne Fédéral
              en: Monday after Federal Day of Thanksgiving, Repentance and Prayer
          12-26: false
      # @source https://www.vs.ch/fr/web/srh/plan-de-travail
      VS:
        names:
          fr: Canton du Valais
          de: Kanton Wallis
          it: Canton Vallese
          en: Canton of Valais
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: Saint-Berthold
            type: bank
          03-19:
            _name: 03-19
          easter -2: false
          easter 1:
            _name: easter 1
            type: optional
          05-01 12:00:
            _name: 05-01
          easter 50:
            _name: easter 50
          easter 60:
            _name: easter 60
          08-15:
            _name: 08-15
          monday after 3rd sunday after 09-01:
            name:
              de: Bettagsmontag
              fr: Lundi du Jeûne Fédéral
              en: Monday after Federal Day of Thanksgiving, Repentance and Prayer
            type: bank
          11-01:
            _name: 11-01
          12-08:
            _name: 12-08
          12-24 12:00:
            _name: 12-24
          12-26:
            type: optional
      NE:
        names:
          fr: Canton de Neuchâtel
          de: Kanton Neuenburg
          it: Canton Neuchâtel
          en: Canton of Neuchâtel
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02 on monday:
            name:
              de: Berchtoldstag
              fr: 2 Janvier
          01-02:
            name:
              de: Berchtoldstag
              fr: 2 Janvier
            type: observance
          03-01:
            name:
              fr: Instauration de la République
              de: Jahrestag Ausrufung der Republik
          easter -2:
            _name: easter -2
          05-01:
            _name: 05-01
          easter 50:
            _name: easter 50
            type: observance
          easter 60:
            _name: easter 60
          monday after 3rd sunday in September:
            name:
              de: Bettagsmontag
              fr: Lundi du Jeûne Fédéral
              en: Monday after Federal Day of Thanksgiving, Repentance and Prayer
            type: optional
          12-26:
            type: observance
          12-26 on monday:
            _name: 12-26
      GE:
        names:
          fr: Canton de Genève
          de: Kanton Genf
          it: Canton Ginevra
          en: Canton of Geneva
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: 2 Janvier
            type: bank
          thursday after 1st sunday after 09-01:
            name:
              de: Genfer Bettag
              fr: Jeûne Genevois
          12-26:
            type: bank
          12-31:
            name:
              de: Wiederherstellung der Republik
              fr: Restauration de la République
              en: Restoration of the Republic
          3rd sunday after 09-01: false
      JU:
        names:
          fr: Canton du Jura
          de: Kanton Jura
          it: Canton Giura
          en: Canton of Jura
        langs:
          - fr
          - de-ch
          - de
          - it
        days:
          01-02:
            name:
              de: Berchtoldstag
              fr: 2 Janvier
          05-01:
            _name: 05-01
          easter 60:
            _name: easter 60
          06-23:
            name:
              fr: Plébiscite jurassien
              en: Jura Plebiscite
              de: Fest der Unabhängigkeit
          08-15:
            _name: 08-15
          11-01:
            _name: 11-01
          12-26: false
