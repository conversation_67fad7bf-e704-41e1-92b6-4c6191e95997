{"name": "date-holidays", "version": "3.24.4", "description": "worldwide holidays", "keywords": ["holidays", "world"], "homepage": "https://github.com/commenthol/date-holidays", "bugs": {"url": "https://github.com/commenthol/date-holidays/issues"}, "repository": {"type": "git", "url": "git+https://github.com/commenthol/date-holidays.git"}, "license": "(ISC AND CC-BY-3.0)", "author": "commenthol <<EMAIL>>", "maintainers": ["commenthol <<EMAIL>>", "<PERSON> <<EMAIL>>"], "contributors": ["0xflotus <<EMAIL>>", "<PERSON> <<EMAIL>>", "Adminy <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <57287326+<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>", "AlinaA <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "ash <<EMAIL>>", "ben <<EMAIL>>", "<PERSON> <<EMAIL>>", "Bilal Gültekin <<EMAIL>>", "bogdanf <<EMAIL>>", "bogdan<PERSON>scu <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Brandon Bay <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "damon02 <<EMAIL>>", "<PERSON> <david<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <f.ansan<PERSON>@solution-g.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "g<PERSON>ri <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "germain-receeve <<EMAIL>>", "gisninaelle <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "havardnyboe <<EMAIL>>", "hermansigue <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Ing.<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "JAlexander <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Šumak <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "jhordyess <<EMAIL>>", "jk-12345 <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <3976145+kev<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Leonidas Villeneuve <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <**************>", "M <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "mirobimbo <<EMAIL>>", "<PERSON> <<EMAIL>>", "Nancy <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "omicron <<EMAIL>>", "oprogramador <<EMAIL>>", "<PERSON> <<EMAIL>>", "osoken <<EMAIL>>", "Pander <<EMAIL>>", "Pandu Supriyono <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "oprogramador <<EMAIL>>", "PPsyrius <<EMAIL>>", "<EMAIL> <<EMAIL>>", "r<PERSON>sco <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "RogueCode007 <<EMAIL>>", "<PERSON> <<EMAIL>>", "RomanTheBrov <romouald.do<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "seb <<EMAIL>>", "Seb735 <duclut<PERSON><PERSON><PERSON>@yahoo.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "ThelenOliver <<EMAIL>>", "Tiago <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <tjn<PERSON><PERSON>@icloud.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "totha<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>-<PERSON> <<EMAIL>>", "Vuo<PERSON> Ho <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "westn <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON>'s Apple Minion <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <zacharias.er<PERSON><PERSON>@gmail.com>", "<PERSON> <zack<PERSON><PERSON>@ymail.com>", "zbypy <<EMAIL>>", "Zhikai <<EMAIL>>"], "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./lib/index.cjs", "types": "./types/index.d.ts"}, "./data": {"import": "./src/data.js", "require": "./lib/data.cjs"}, "./package.json": "./package.json"}, "main": "./lib/index.cjs", "module": "./src/index.js", "types": "./types", "bin": {"holidays2json": "scripts/holidays2json.cjs"}, "directories": {"lib": "lib", "doc": "docs", "test": "test"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["break", "feat", "fix", "chore", "docs", "refactor", "revert", "test"]], "subject-case": [2, "never", ["start-case", "pascal-case"]], "scope-case": [0]}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "mocha": {"checkLeaks": true, "colors": true, "reporter": "dot"}, "dependencies": {"date-holidays-parser": "^3.4.7", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "prepin": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.27.0", "@babel/core": "^7.26.10", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.26.9", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@mocha/contributors": "git+https://github.com/commenthol/contributors.git#semver:1.1.0-0", "babel-loader": "^9.2.1", "dtslint": "^4.2.1", "eslint": "^8.57.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-yml": "^0.15.0", "hashtree": "^0.7.0", "husky": "^9.1.7", "markedpp": "^1.4.0", "mocha": "^11.2.2", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "rollup": "^2.79.2", "typescript": "^5.8.3", "watch-run": "^1.2.5", "webpack": "^5.99.5", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=12.0.0"}, "c4uIgnore": {"eslint-plugin-yml": "^0.15.0 // newer versions do too much checking", "rollup": "^2.79.1 // v3 changed exports; needs refactoring first."}, "scripts": {"all": "npm-run-all clean build lint test doc:tree doc:attrib webpack", "build": "npm-run-all yaml build:only", "build:only": "rollup -c", "changelog": "contributors && node scripts/gitlog.cjs", "ci": "TEST_XXL=1 npm-run-all yaml build test", "clean": "rimraf lib dist src/data.js", "clean:all": "npm-run-all clean clean:modules", "clean:modules": "rimraf node_modules", "doc:attrib": "node scripts/attributions.cjs", "doc:tree": "node scripts/addtree.cjs", "lint": "eslint --fix --ext .js,.cjs,.yaml .", "test": "npm-run-all test:ci", "test:ci": "mocha", "test:ts": "dtslint types", "watch": "watch-run -p data/countries/*.yaml npm run yaml", "webpack": "webpack", "webpack:analyze": "webpack", "yaml": "node scripts/holidays2json.cjs"}}