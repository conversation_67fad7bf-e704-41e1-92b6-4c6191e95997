const holidayService = require('./src/services/holidayService');

async function testHolidayService() {
  console.log('Testing Holiday Service...\n');

  try {
    // Test Philippine holidays
    console.log('=== Testing Philippine Holidays ===');
    const philippineHolidays = holidayService.getPhilippineHolidays(2024);
    console.log(`Found ${philippineHolidays.length} Philippine holidays for 2024:`);
    philippineHolidays.slice(0, 5).forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date}) - ${holiday.type}`);
    });
    console.log('...\n');

    // Test international holidays
    console.log('=== Testing International Holidays ===');
    const internationalHolidays = holidayService.getInternationalHolidays(2024);
    console.log(`Found ${internationalHolidays.length} international holidays for 2024:`);
    internationalHolidays.slice(0, 5).forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date}) - ${holiday.country_code}`);
    });
    console.log('...\n');

    // Test religious holidays
    console.log('=== Testing Religious Holidays ===');
    const religiousHolidays = holidayService.getReligiousHolidays(2024);
    console.log(`Found ${religiousHolidays.length} religious holidays for 2024:`);
    religiousHolidays.slice(0, 5).forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date}) - ${holiday.subcategory || holiday.category}`);
    });
    console.log('...\n');

    // Test additional holidays
    console.log('=== Testing Additional Holidays ===');
    const additionalHolidays = holidayService.getAdditionalHolidays(2024);
    console.log(`Found ${additionalHolidays.length} additional holidays for 2024:`);
    additionalHolidays.forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date}) - ${holiday.category}`);
    });
    console.log('...\n');

    // Test all holidays combined
    console.log('=== Testing All Holidays Combined ===');
    const allHolidays = await holidayService.getAllHolidays(2024);
    console.log(`Found ${allHolidays.length} total holidays for 2024 after deduplication`);
    
    // Group by category
    const byCategory = {};
    allHolidays.forEach(holiday => {
      const category = holiday.category || 'Unknown';
      byCategory[category] = (byCategory[category] || 0) + 1;
    });
    
    console.log('\nHolidays by category:');
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`- ${category}: ${count} holidays`);
    });

    console.log('\n=== Sample holidays from each month ===');
    const monthlyHolidays = {};
    allHolidays.forEach(holiday => {
      const month = new Date(holiday.date).getMonth() + 1;
      if (!monthlyHolidays[month]) {
        monthlyHolidays[month] = [];
      }
      monthlyHolidays[month].push(holiday);
    });

    Object.keys(monthlyHolidays).sort((a, b) => a - b).forEach(month => {
      const monthName = new Date(2024, month - 1, 1).toLocaleString('default', { month: 'long' });
      console.log(`\n${monthName} (${monthlyHolidays[month].length} holidays):`);
      monthlyHolidays[month].slice(0, 3).forEach(holiday => {
        console.log(`  - ${holiday.name} (${holiday.date}) [${holiday.category}]`);
      });
      if (monthlyHolidays[month].length > 3) {
        console.log(`  ... and ${monthlyHolidays[month].length - 3} more`);
      }
    });

  } catch (error) {
    console.error('Error testing holiday service:', error);
  }
}

testHolidayService();
