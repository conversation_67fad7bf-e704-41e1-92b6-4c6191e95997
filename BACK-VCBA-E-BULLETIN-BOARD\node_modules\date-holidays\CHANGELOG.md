# Changelog

## [3.24.4](https://github.com/commenthol/date-holidays/compare/3.24.3...3.24.4) (2025-06-12)

- fix(HK): yaml liniting [22b41c15](https://github.com/commenthol/date-holidays/commit/22b41c15a6c9fc603c9360f21c7a8ec6ceab162d)
- fix(SA): Correct Eid al-Fitr dates and update test fixtures [5aca765d](https://github.com/commenthol/date-holidays/commit/5aca765dab6bf20c2ae337223dcde6ec829a69e2)
- feat(AR): Add one-off holidays [8cbd51b3](https://github.com/commenthol/date-holidays/commit/8cbd51b3a687f53badd4a96165af437ce1d425b9)
- fix(JP):Changed from Noel to Christmas [116d3dcf](https://github.com/commenthol/date-holidays/commit/116d3dcf9bc97c5e5fccc63c68a85541d1a114f4)
- fix(HK): Fix Hong Kong holidays [9ec080c4](https://github.com/commenthol/date-holidays/commit/9ec080c4ce68e0d6b77e53b6444345bcc0cdc70d)

## [3.24.3](https://github.com/commenthol/date-holidays/compare/3.24.2...3.24.3) (2025-05-12)

- fix(VA): new pope leo xiv [c01eb7d3](https://github.com/commenthol/date-holidays/commit/c01eb7d3123d52080f877fb986b3f2832609a383)
- fix(SG): Vesak Day 2025 [046f571d](https://github.com/commenthol/date-holidays/commit/046f571d4d992261f86cbd380235f082fa36749d)
- fix(DE-HE): Remove Easter and Pentecost Sundays from DE-HE [0ec41ec5](https://github.com/commenthol/date-holidays/commit/0ec41ec567ae1183907e5a6fcabec9106feae38e)

## [3.24.2](https://github.com/commenthol/date-holidays/compare/3.24.1...3.24.2) (2025-04-18)

- Merge branch 'sk-constitution-day' [58bdda13](https://github.com/commenthol/date-holidays/commit/58bdda1372073b71fafbebf833d94f05524612de)
- fix(SK): constitution day is not public holiday [0230bc64](https://github.com/commenthol/date-holidays/commit/0230bc64596e69041e1f6582e9361d8244e80426)
- fix(CA-NB, #506): family day [c5f95f30](https://github.com/commenthol/date-holidays/commit/c5f95f30f0b1dea140678e3bdd3e9494c65bd29d)

## [3.24.1](https://github.com/commenthol/date-holidays/compare/3.24.0...3.24.1) (2025-03-16)

- fix(LI): correct spelling of Liechtenstein [a4631b7e](https://github.com/commenthol/date-holidays/commit/a4631b7e5e8f540e13120a5c40d3e08f83ea488e)

## [3.24.0](https://github.com/commenthol/date-holidays/compare/3.23.22...3.24.0) (2025-03-08)

- feat(SA): Add Saudi Arabia holidays [494349f3](https://github.com/commenthol/date-holidays/commit/494349f387f08d387e63251c007fcb8b46605338)
- feat(AR): Update Argentine holiday data comprehensively (1976-2025) [419cfcca](https://github.com/commenthol/date-holidays/commit/419cfcca1a2ae2cea2f5389924fd12f7997c6697)
- feat(IT): Added holiday data for RM Lazio Saints Peter and Paul [212fa34d](https://github.com/commenthol/date-holidays/commit/212fa34dac5a9f8cee55e6f3d2aa78955138214b)
- ci: add workflow_dispatch trigger to run CI on any commit [22859900](https://github.com/commenthol/date-holidays/commit/2285990094adbc632be918d04869e996bafb2eb5)

## [3.23.22](https://github.com/commenthol/date-holidays/compare/3.23.21...3.23.22) (2025-02-16)

- fix(MX): correct Mexican holiday definitions, prevent duplicate occurrences. [ce467d88](https://github.com/commenthol/date-holidays/commit/ce467d888ea0e822c57e1fd4918937b9cb8453a9)

## [3.23.21](https://github.com/commenthol/date-holidays/compare/3.23.20...3.23.21) (2025-02-09)

- fix(SG): Public holidays 2025 update [f240bce6](https://github.com/commenthol/date-holidays/commit/f240bce69381f39cf1600f23b92f8ea776b799a4)
- fix(SG): Update dates of SG public holidays hari raya [7fee91d3](https://github.com/commenthol/date-holidays/commit/7fee91d343e67c7d39e545a9f9444f038bdb595d)
- fix(PL): yaml whitespace [32acb999](https://github.com/commenthol/date-holidays/commit/32acb99952f986fbaa364273b29eef2f49751da0)

## [3.24.0](https://github.com/commenthol/date-holidays/compare/3.23.19...3.24.0) (2025-02-01)

- feat(PL, #512): add non-public holidays [2282059c](https://github.com/commenthol/date-holidays/commit/2282059c5e1ef2a2c3f7279913e7cc6bb081f729)
- test: extend fixtures to 2028, 2029 [81a2fabe](https://github.com/commenthol/date-holidays/commit/81a2fabe47bc994644148ed38292c1e0be652846)
- fix(DE-BE): 75th anniversary of the GDR uprising [89481547](https://github.com/commenthol/date-holidays/commit/89481547590369632bb46b368641d6700db59a48)

## [3.23.19](https://github.com/commenthol/date-holidays/compare/3.23.18...3.23.19) (2025-02-01)

- test(ID): Apply test for ID Holiday 2025 until 2027 [f271b3f2](https://github.com/commenthol/date-holidays/commit/f271b3f2b13424d0e9c27b0c404debb6d9d9126f)
- fix(ID): Update Nyepi Holiday 2027 [0b6f5ffd](https://github.com/commenthol/date-holidays/commit/0b6f5ffd2ef1c165adba38452d1e311fc0f4b8da)
- fix(ID): Add Nyepi Holiday dates until 2027 [129d3ecd](https://github.com/commenthol/date-holidays/commit/129d3ecd0e9351d0cc4edd17e2aaa673335627c5)
- fix(ID): Add Waisak holiday dates until 2027 [947c37ce](https://github.com/commenthol/date-holidays/commit/947c37ce28aa5e5dde8bacbaf290062cb48fbdea)
- fix(PL, #507): add Christmas Eve as public holiday since 2025 [737ef6ff](https://github.com/commenthol/date-holidays/commit/737ef6ffb0cd2e869d05b6467626600be3f142fd)
- fix(DE-BE): add liberation day 2025 for DE-BE [c480c86f](https://github.com/commenthol/date-holidays/commit/c480c86f48b7b37d6f17622cba4ee03b0c2dffe5)

## [3.23.18](https://github.com/commenthol/date-holidays/compare/3.23.17...3.23.18) (2025-01-08)

- fix(NL): state and region names [246cd650](https://github.com/commenthol/date-holidays/commit/246cd650f98060a84e9dd585b408b4287165ed28)
- fix(IE): remove bank holiday and update test fixtures [efc0475d](https://github.com/commenthol/date-holidays/commit/efc0475d666b0849424b23028ed382b83bbc9d19)
- fix(NL): additional fix and translations Dutch [2fe1276d](https://github.com/commenthol/date-holidays/commit/2fe1276d85ee82661d17d61a87e0c790d30426ad)
- fix(NL): Dutch states [d26b9b5e](https://github.com/commenthol/date-holidays/commit/d26b9b5e93d7453f535ffedfd2844fd3fd72c1dc)

## [3.23.17](https://github.com/commenthol/date-holidays/compare/3.23.16...3.23.17) (2025-01-02)

- chore: update github actions to v4; deprecate node@18 [936662ff](https://github.com/commenthol/date-holidays/commit/936662ff8f25fd15d20dee7b579bf7d256ca7d57)
- test: Update fixtures [3b8ad77f](https://github.com/commenthol/date-holidays/commit/3b8ad77fb0a317ff14d6b27e43b9d1f3d0d12422)
- fix(NL): attributions and translations Dutch holidays [ebf3f196](https://github.com/commenthol/date-holidays/commit/ebf3f196e1a82d6652affa456439db7b4ff4d414)
- chore: fix status badge [e925b293](https://github.com/commenthol/date-holidays/commit/e925b2939f2b480b853679b3e426e9acea68409f)
- fix(RO): Update RO data (#1) [b6ea8b56](https://github.com/commenthol/date-holidays/commit/b6ea8b569d3a7ef11475122a5a81dbf6ff9092df)

## [3.23.16](https://github.com/commenthol/date-holidays/compare/3.23.15...3.23.16) (2024-12-27)

- fix(GB-SCT,#494): add substitute day for st andrew's day [8f0fb474](https://github.com/commenthol/date-holidays/commit/8f0fb474f843b1e81423276f741905999906698d)
- fix(IE,#495): removal of Christmas bank holiday [930fd6ee](https://github.com/commenthol/date-holidays/commit/930fd6ee07a0aa3b628d84124984f05651b453d3)

## [3.23.15](https://github.com/commenthol/date-holidays/compare/3.23.14...3.23.15) (2024-12-27)

- fix(BR): Fixes indentation and fixtures json based on fix [3ee04a9f](https://github.com/commenthol/date-holidays/commit/3ee04a9fc7b1d93a1c7ba9449f50d841ebe6c861)
- fix(BR): Updates Black Awareness for BR since it is a national holiday beginning on 2024 [851f7fb8](https://github.com/commenthol/date-holidays/commit/851f7fb889fe744aaff2c643deb5629bd96dfb02)

## [3.23.14](https://github.com/commenthol/date-holidays/compare/3.23.13...3.23.14) (2024-12-12)

- fix(IT): state names [c3daf4c4](https://github.com/commenthol/date-holidays/commit/c3daf4c41d0a1532ba23c3fc6762b490abad14e5)
- feat(IT): add Florence patron saint holiday [********](https://github.com/commenthol/date-holidays/commit/********d5927d5b1ee040dde46ae47852c1e48b)
- docs: fix description on calling holidays2json.cjs in README [c674a6ab](https://github.com/commenthol/date-holidays/commit/c674a6abf37fdc5dfba37f0c1c4f0e99694ee959)

## [3.23.13](https://github.com/commenthol/date-holidays/compare/3.23.12...3.23.13) (2024-11-26)

- fix(AU): correct Kings Birthday in Western Australia for 2024 [6a81888e](https://github.com/commenthol/date-holidays/commit/6a81888e56ecab97094183578ea4fb0630e90da0)
- fix(CL): enable june solstice holiday since 2022 [e8e98e1f](https://github.com/commenthol/date-holidays/commit/e8e98e1fc1faf65f5de2bffeb07cd23e5b244d94)
- fix(CL): changed fixed date holiday june 21st to june solstice [b9417a8d](https://github.com/commenthol/date-holidays/commit/b9417a8d924de879fdb39a0e2305fcedf96bf287)
- chore: add node@22 [65b10b7b](https://github.com/commenthol/date-holidays/commit/65b10b7b38585320e111696bf88a4b2461416c88)

## [3.23.12](https://github.com/commenthol/date-holidays/compare/3.23.11...3.23.12) (2024-05-04)

- test(BQ,CW,SR): fix fixtures [70dbb2d3](https://github.com/commenthol/date-holidays/commit/70dbb2d3142566cf1580be37159f08439713e7c2)
- fix(NL): improved Dutch translations [b6ae4901](https://github.com/commenthol/date-holidays/commit/b6ae4901248d137f1e8335ce066d83c2baed0198)
- fix(names): added World Animal Day [38de6fda](https://github.com/commenthol/date-holidays/commit/38de6fda1d56cf537d92b33aa96c52052ea9478a)

## [3.23.11](https://github.com/commenthol/date-holidays/compare/3.23.10...3.23.11) (2024-04-17)

- add corpus christi holiday in São Paulo, SP, BR [7cedeeb8](https://github.com/commenthol/date-holidays/commit/7cedeeb8a8dcde71cf4eeedda7ecefec723ee2c6)
- Add Voting Day to ZA-2024.json [23580f19](https://github.com/commenthol/date-holidays/commit/23580f19a2bef807ed8c04657d83bb28313643ba)
- chore: run yaml before build [1ee2e823](https://github.com/commenthol/date-holidays/commit/1ee2e8239ce444ff1f0354be5430c0a9da9e47dd)
- Remove `string` from HolidayType, so that autocomplete works [3d00852a](https://github.com/commenthol/date-holidays/commit/3d00852a5f250e2340bd2c597406d5509bc689f2)
- Added ZA public holiday for Voting Day on May 29, 2024 [bfae8b17](https://github.com/commenthol/date-holidays/commit/bfae8b17d74918f341ac2af409c3ed03def35fd4)

## [3.23.10](https://github.com/commenthol/date-holidays/compare/3.23.9...3.23.11) (2024-03-14)

- fix(HU,#464): update the name of the public holiday on March 15 [3d594188](https://github.com/commenthol/date-holidays/commit/3d5941884867a4f000203efde80784dc295c7ec5)

## [3.23.9](https://github.com/commenthol/date-holidays/compare/3.23.8...3.23.9) (2024-02-29)

- fix(SG): Vesak Day 2024 [8940db8f](https://github.com/commenthol/date-holidays/commit/8940db8fb9c2e3a4b23fc57258da1397244ed1cf)
- fix(SG): Hari Raya Haji day 2024 [a33ff72a](https://github.com/commenthol/date-holidays/commit/a33ff72a1b803f59b2ff1c8cea755d053a285ccc)

## [3.23.8](https://github.com/commenthol/date-holidays/compare/3.23.7...3.23.8) (2024-02-08)

- fix(AT,#455): josefitag set to type school [8adc4b72](https://github.com/commenthol/date-holidays/commit/8adc4b7239bbd79d401544298ffcb26277592c08)
- fix(SG): Fix Singapore Hari Raya Puasa date for 2024 - Update SG.yaml [45a75e8f](https://github.com/commenthol/date-holidays/commit/45a75e8fd0c5da4e36ddcd76b6322303ece9f290)

## [3.23.7](https://github.com/commenthol/date-holidays/compare/3.23.6...3.23.7) (2024-01-25)

- fix(SE): Update SE.yaml with english translations [b3f73b13](https://github.com/commenthol/date-holidays/commit/b3f73b13b77255510ae178f487d5073d0ad2c765)

## [3.23.6](https://github.com/commenthol/date-holidays/compare/3.23.5...3.23.6) (2024-01-25)

- fix(#452): replace lodash.pick with lodash [d511e669](https://github.com/commenthol/date-holidays/commit/d511e669381b42f50e8d305fc1a4fc14ca947a2e)

## [3.23.5](https://github.com/commenthol/date-holidays/compare/3.23.4...3.23.5) (2024-01-20)

- chore: disabling dtslint temporarily [a7c91e52](https://github.com/commenthol/date-holidays/commit/a7c91e52e94d5f19cfc7d011b3cc4fd6c5d0df62)
- fix(HU): good-friday is public holiday since 2017 [ebe4a95c](https://github.com/commenthol/date-holidays/commit/ebe4a95c60c7656c223ac58873444b8724f821d2)

## [3.23.4](https://github.com/commenthol/date-holidays/compare/3.23.3...3.23.4) (2024-01-20)

- fix(ZA): Add ZA Rugby World Cup Win Public Holiday [b169fcfb](https://github.com/commenthol/date-holidays/commit/b169fcfbf01dd07b34fac82547674fe9cd8cc34a)

## [3.23.3](https://github.com/commenthol/date-holidays/compare/3.23.2...3.23.3) (2023-12-10)

- fix(BR): Add black conscience day as a regional holiday  [8689366a](https://github.com/commenthol/date-holidays/commit/8689366a432e9ccf7b734a087e46ab7b671b143f)
- fix(NO): fixed sundays fixed sundays in advent for norway [86f0f5a5](https://github.com/commenthol/date-holidays/commit/86f0f5a5aec51d88960baa92026ff408ed66502b)
- fix(BO): Correct holiday type for Bolivia [6f9d4c04](https://github.com/commenthol/date-holidays/commit/6f9d4c04505cfe91dce5cd55905cf7fe65c31a5a)

## [3.23.2](https://github.com/commenthol/date-holidays/compare/3.23.1...3.23.2) (2023-11-25)

- fix(GL,EG): DST time changes [58e8ffc9](https://github.com/commenthol/date-holidays/commit/58e8ffc9534502053bec1ec21b86e76645019a5c)
- fix: add missing `y` to January 2nd holiday [c3041273](https://github.com/commenthol/date-holidays/commit/c304127352af8ca9fd5d99e4cd6cb2115224c805)

## [3.23.1](https://github.com/commenthol/date-holidays/compare/3.23.0...3.23.1) (2023-11-04)

- fix(HU): Adding school holidays till the end of Jan 2024 [27b2071a](https://github.com/commenthol/date-holidays/commit/27b2071a335455a15a32edabcae476bfdf1fdcd5)
- fix(NL): Improve holiday definitions for The Netherlands [8adc8a42](https://github.com/commenthol/date-holidays/commit/8adc8a424ad8e60fac40cf51e2457f4a6a2115dc)

## [3.23.0](https://github.com/commenthol/date-holidays/compare/3.22.1...3.23.0) (2023-10-18)

- fix: Changed source linking and attributions [f218d1c9](https://github.com/commenthol/date-holidays/commit/f218d1c9a55eb257e1206d5cf5e11a20e25f6a5d)
- feat: Updated for Bolivian holidays [5b54bdda](https://github.com/commenthol/date-holidays/commit/5b54bdda0aa6ced57c416bc61d32eca5434e2e1a)
- fix: modify `Queen's Birthday` to `King's Birthday` in Australia [a4435a76](https://github.com/commenthol/date-holidays/commit/a4435a767b763eec341e8fef06b079a1bf9bbf8b)

## [3.22.1](https://github.com/commenthol/date-holidays/compare/3.22.0...3.22.1) (2023-10-05)

- fix(US): modify holiday name for DC, ME, NM with sources [375740be](https://github.com/commenthol/date-holidays/commit/375740bea1d8cf99b74baff2035ef9069843a6e7)
- fix(US): modify apostrophe character for MN holiday [b1c9617e](https://github.com/commenthol/date-holidays/commit/b1c9617eb2cc3eb58d9cdc31f166a2909f8e9abc)
- fix(US): change Columbus Day to Indigenous Peoples Day in US.IA since 2018 [06db084a](https://github.com/commenthol/date-holidays/commit/06db084a0d5918e4ec161943006aafeff8e25fcf)

## [3.22.0](https://github.com/commenthol/date-holidays/compare/3.21.5...3.22.0) (2023-09-14)

- feat(UA): Update ukrainian holidays [91d8a1a3](https://github.com/commenthol/date-holidays/commit/91d8a1a340df853720b01b19eaaf9cce78d683ef)

## [3.21.5](https://github.com/commenthol/date-holidays/compare/3.21.4...3.21.5) (2023-08-27)

- fix(AT): yaml error [afbe5f00](https://github.com/commenthol/date-holidays/commit/afbe5f00ce6b7ffbb636e0fe02da6fb510c44f9b)
- chore: update license attributions [8fe7d2d0](https://github.com/commenthol/date-holidays/commit/8fe7d2d0b2d216c16310d0dc5a21e286bff35302)
- Make Leopoldi-Tag optional , add notes and source [8e02b295](https://github.com/commenthol/date-holidays/commit/8e02b295600a6161cefb90f3ab3a37b02c6ceb60)

## [3.21.4](https://github.com/commenthol/date-holidays/compare/3.21.3...3.21.4) (2023-08-25)

- fix(SG): added SG presidential election polling day 2023 [a7211a9a](https://github.com/commenthol/date-holidays/commit/a7211a9a18547e2375ed412d09c5d21d82035ee4)

## [3.21.3](https://github.com/commenthol/date-holidays/compare/3.21.2...3.21.3) (2023-08-16)

- fix(BE-VLG,#424): add boxing day as observance [c03b59b6](https://github.com/commenthol/date-holidays/commit/c03b59b6227506e17429dfcbe809bdeacdcf0585)

## [3.21.2](https://github.com/commenthol/date-holidays/compare/3.21.1...3.21.2) (2023-06-24)

- fix(AX): midsummer should use same logic as SE and FI [c414c4d1](https://github.com/commenthol/date-holidays/commit/c414c4d1c5316c69025e272ed7bdc322ff395e28)
- fix(TH): Fixing wrong translation for Songkran Day [f80e18d8](https://github.com/commenthol/date-holidays/commit/f80e18d882f86bcf9781b6d9d9fb5ec4f750e095)

## [3.21.1](https://github.com/commenthol/date-holidays/compare/3.21.0...3.21.1) (2023-06-08)

- fix(CA): modify holiday name [48bc62d2](https://github.com/commenthol/date-holidays/commit/48bc62d26276ab594ca26bf88e0a9accbcc17fb6)

## [3.21.0](https://github.com/commenthol/date-holidays/compare/3.20.0...3.21.0) (2023-06-03)

- feat(LV): Bronze medal of the Latvian hockey team at the 2023 World Hockey Championship [26e1b9ef](https://github.com/commenthol/date-holidays/commit/26e1b9ef52d3e2e149de8198cacab76afd9735a7)

## [3.20.0](https://github.com/commenthol/date-holidays/compare/3.19.2...3.20.0) (2023-06-03)

- fix: last observed 2020, so prior to 2021 [6397ed83](https://github.com/commenthol/date-holidays/commit/6397ed83ec0d9dc1866256e0ec4c2dc5b175f4b7)
- fix: actually since 2020 [7da8dc08](https://github.com/commenthol/date-holidays/commit/7da8dc0840197a18700acdbda3f0a56d2f752d47)
- feat: remove Lee-Jackson Day starting 2023, add Election Day [a70b0819](https://github.com/commenthol/date-holidays/commit/a70b0819f42e63656821098b24a8713c3a1d1f4f)
- fix: add start date [523f7cd5](https://github.com/commenthol/date-holidays/commit/523f7cd5b8b57097eab27e8c199f9741b8785d6b)
- fix: re-add for dates prior to 2016 [249d7a91](https://github.com/commenthol/date-holidays/commit/249d7a91b78f2034d916af657acd4fddbf745e96)
- feat: remove service reduction day in US.MD [64519cf4](https://github.com/commenthol/date-holidays/commit/64519cf4e5867666c5036ee022c26409e16179a0)
- Fixed tests after changes to kings birthday [d9d40ee9](https://github.com/commenthol/date-holidays/commit/d9d40ee9b068ca0c24baab56f8658d9d510ccfc6)
- Update NO.yaml [9a94fffd](https://github.com/commenthol/date-holidays/commit/9a94fffd87a01132f1c3d028d56d9dddee3f6fb3)

## [3.19.2](https://github.com/commenthol/date-holidays/compare/3.19.1...3.19.2) (2023-05-12)

- test: add test fixtures for years 2026 2027 [0207f723](https://github.com/commenthol/date-holidays/commit/0207f7234e109ca9351d55af4a3f553ed668f48f)
- test(GL): fix timezone changes [9092c7ad](https://github.com/commenthol/date-holidays/commit/9092c7adea77774638f79b364120f33726606801)
- chore: tslint add no-redundant-jsdoc false [66c1afa4](https://github.com/commenthol/date-holidays/commit/66c1afa48c4fe357e45255595c0ddf232fb3a2ee)
- chore(actions): add node@20 [2d75f5b8](https://github.com/commenthol/date-holidays/commit/2d75f5b8409eca4fde3bb6f910e66e0b15066b35)
- test(EG): fix tests as of moment-timezone update [9988e708](https://github.com/commenthol/date-holidays/commit/9988e708af4b2f92a971bae8b74f0c0a082bb481)

## [3.19.1](https://github.com/commenthol/date-holidays/compare/3.19.0...3.19.1) (2023-05-11)

- test(NZ): fix tests for King's Birthday which is active since 2023 [ae25c892](https://github.com/commenthol/date-holidays/commit/ae25c8927431a91ac1665436959a9dc57ad41db5)
- fix(NZ): Update Queens Birthday to Kings Birthday [ba42e023](https://github.com/commenthol/date-holidays/commit/ba42e02347b03a2a6ec22594bbbede835c53322e)

## [3.19.0](https://github.com/commenthol/date-holidays/compare/3.18.0...3.19.0) (2023-03-12)

- docs: adding Thailand; updating attribution [58076aa2](https://github.com/commenthol/date-holidays/commit/58076aa20304f170c1b9295aa240cbe07405db94)
- feat(TH): Add names for New year & New Year's Eve [5b0b9b0c](https://github.com/commenthol/date-holidays/commit/5b0b9b0c640a2cb5b0f474f9c98292dc9750221d)
- feat(TH): Add Thailand holidays [44df7acd](https://github.com/commenthol/date-holidays/commit/44df7acdac4eb8df361100cf56dcd3beabc7ab47)

## [3.18.0](https://github.com/commenthol/date-holidays/compare/3.17.0...3.18.0) (2023-03-04)

- feat(BR): add cidade maravilhosa, Rio de Janeiro, Brazil, municipal holidays! [4d790b72](https://github.com/commenthol/date-holidays/commit/4d790b722be944299a77810610bc3f40c0f1a768)

## [3.17.0](https://github.com/commenthol/date-holidays/compare/3.16.17...3.17.0) (2023-03-04)

- feat(IT): saint ambrose day milan italy [ef619b8b](https://github.com/commenthol/date-holidays/commit/ef619b8ba71413817cb0a2c5e2ec1b957c6d4b03)
- Update IT.yaml [234db9e6](https://github.com/commenthol/date-holidays/commit/234db9e6067653f5c9abe2a2e1593716cd720291)
- feat(DK): Removed Prayer Day from Denmark from 2024 [bf5c515f](https://github.com/commenthol/date-holidays/commit/bf5c515fc6c405b58fba67cda711cdd23e98b44d)
- added swedish translation for Father's day #401 [23df93e8](https://github.com/commenthol/date-holidays/commit/23df93e8efc76a5ad464fa05e0cd20f7ea100068)
- fix(NO): fixed typo in NO.yaml [1e87d833](https://github.com/commenthol/date-holidays/commit/1e87d833e1254a801dc405d92b8fcdbe6ea7f21d)
- feat(NO): added missing norwegian holidays [fa734db8](https://github.com/commenthol/date-holidays/commit/fa734db807b34591ca23188c214893d2e9c17071)

## [3.16.17](https://github.com/commenthol/date-holidays/compare/3.16.16...3.16.17) (2023-02-11)

- fix(IE): new public holiday St. Brigid's Day [98a619ec](https://github.com/commenthol/date-holidays/commit/98a619ecbc609c6f8bd5afe3d8a72b27f8cadaab)

## [3.16.16](https://github.com/commenthol/date-holidays/compare/3.16.15...3.16.16) (2023-02-11)

- vesak day 2023 for sg [e0ae1ec3](https://github.com/commenthol/date-holidays/commit/e0ae1ec3d959663c3fe750c7ff2eafe002c2f9a9)
- Fix Vesak Day 2023 for Singapore [2e6560ff](https://github.com/commenthol/date-holidays/commit/2e6560ff0ec5143eb1dbc6f7061928b7cb762258)

## [3.16.15](https://github.com/commenthol/date-holidays/compare/3.16.14...3.16.15) (2023-01-11)

- fix(#392,FI): fix Finland's incorrect New Year's Eve rule [a0b69a24](https://github.com/commenthol/date-holidays/commit/a0b69a24f0622e1f76445d1886779ca899f7d170)
- fix(IE): 2nd of january and 27th of dec is a bank holiday [98222af3](https://github.com/commenthol/date-holidays/commit/98222af308af906f3aaa0c48a1bd1851059e4c81)

## [3.16.14](https://github.com/commenthol/date-holidays/compare/3.16.13...3.16.14) (2023-01-05)

- fix(CL): Adds 08-10 as observance holiday for CL-TA [7bb7e3d8](https://github.com/commenthol/date-holidays/commit/7bb7e3d80deb4aee085a010052fb34c20320e379)
- fix(CL): Fixes wording on 06-29 holiday offsets [9753c1df](https://github.com/commenthol/date-holidays/commit/9753c1df2942d0a270879cbc47de7d390c8c163f)
- fix(CL): Removes one-off holiday from CL-TA fixtures from 2016 onwards [202b8c9f](https://github.com/commenthol/date-holidays/commit/202b8c9f0351b994b09512fcf7b1cbee94436051)
- chore: Adds holidays for 2023 CL [a81e9a53](https://github.com/commenthol/date-holidays/commit/a81e9a53e6211a20ee5f1e66c3cdb6cc19c89c3a)

## [3.16.13](https://github.com/commenthol/date-holidays/compare/3.16.12...3.16.13) (2022-12-17)

- fix(GL): moment timezone update [f7824b2c](https://github.com/commenthol/date-holidays/commit/f7824b2c9d3b14926cdc0758507d19d321f6459e)
- fix(SG,#387): correct 2023 holidays [dc13d37b](https://github.com/commenthol/date-holidays/commit/dc13d37b952b476ccff19aa7159f4113131db06e)

## [3.16.12](https://github.com/commenthol/date-holidays/compare/3.16.11...3.16.12) (2022-12-10)

- fix(GB): Added King Charles III's Coronation [0b07976a](https://github.com/commenthol/date-holidays/commit/0b07976af0f37d844f7ede92b9c74110be7f10f5)
- fix(LU): added a source for Luxembourg [f46c5f45](https://github.com/commenthol/date-holidays/commit/f46c5f45ebe1b7563245c2af7b300ad7317a5331)

## [3.16.11](https://github.com/commenthol/date-holidays/compare/3.16.10...3.16.11) (2022-11-20)

- test(MX,FJ): fix tests due to timezone update [9c961f7a](https://github.com/commenthol/date-holidays/commit/9c961f7a22b89a6285ec3a0f6dd5f3219d986b0b)
- Improved support for public holidays in Luxembourg - added translations in Luxembourgish (lb) - added active dates - renamed 06-23 into National Holiday ("L'anniversaire du Grand-Duc" is not the official name and is currently not true) [80316399](https://github.com/commenthol/date-holidays/commit/80316399e5276378e5eb90636dca8063347547fe)
- chore: update actions [3d7edfc5](https://github.com/commenthol/date-holidays/commit/3d7edfc5d041a7ae0138edbaefcdebd801d0a890)

## [3.16.10](https://github.com/commenthol/date-holidays/compare/3.16.9...3.16.10) (2022-11-11)

- test(LT): fix tests [6d1925b4](https://github.com/commenthol/date-holidays/commit/6d1925b49aa081d3bf9dde4c5640076614c3ab30)
- test(MD,RO): fix tests [e072a88b](https://github.com/commenthol/date-holidays/commit/e072a88b26856f7a8712e2de6c06d6d37f344ab2)
- add changes [5825446a](https://github.com/commenthol/date-holidays/commit/5825446a937e9d89ff784323c0d5b6a0fc128bb5)
- Correct Romanian names for some holidays [3d4f518f](https://github.com/commenthol/date-holidays/commit/3d4f518fca2134898e742447d0a5aeb7d92330b4)

## [3.16.9](https://github.com/commenthol/date-holidays/compare/3.16.8...3.16.9) (2022-11-02)

- fix(DE-HE,#361): easter and pentecost are public holidays [93a6213c](https://github.com/commenthol/date-holidays/commit/93a6213c4deee649b895bb08d7d8fb6cd6033e38)

## [3.16.8](https://github.com/commenthol/date-holidays/compare/3.16.7...3.16.8) (2022-10-22)

- Update Ireland to show the correct October holiday [37591e5b](https://github.com/commenthol/date-holidays/commit/37591e5bf579157eb03329cae2f34d78648341f8)

## [3.16.7](https://github.com/commenthol/date-holidays/compare/3.16.6...3.16.7) (2022-09-15)

- chore: npmrc strict-version and ignore-scripts set [e14212ef](https://github.com/commenthol/date-holidays/commit/e14212efdc74b2ea8a9e31d81fce32c1bcdf1e50)
- fix(NZ): 2022-09-26 National Day of Mourning for Queen Elizabeth II [aa363e07](https://github.com/commenthol/date-holidays/commit/aa363e071e91e8ad08937c0183bd2dce0818e511)
- fix(ES): various fixes for state holidays [81a9ef8a](https://github.com/commenthol/date-holidays/commit/81a9ef8a5d093dde80f0b02522cf1b9823adb8d4)

## [3.16.6](https://github.com/commenthol/date-holidays/compare/3.16.5...3.16.6) (2022-09-11)

- fix(CL): Fixes holiday dates for Chile in september, october [ceab3db9](https://github.com/commenthol/date-holidays/commit/ceab3db9d2a266616a0568e6359f33b9cd79aea6)
- fix(IR): fix failing tests [77d5f119](https://github.com/commenthol/date-holidays/commit/77d5f119df077f0e0872c7cc1efb0d73d0a81d2b)
- fix(AU): 2022-09-22 National Day of Mourning for Queen Elizabeth II [ff885563](https://github.com/commenthol/date-holidays/commit/ff88556360b743c02b6df4540157a101873f32f7)
- fix(GB,GG,GI,IM,JE): 2022-09-19 Queen Elizabeth's Funeral Day [511d65bf](https://github.com/commenthol/date-holidays/commit/511d65bf96cd7ff1d91390a69614c3cf55ee6aef)
- chore: Fixes holiday dates for Chile on september, october [ceab3db9](https://github.com/commenthol/date-holidays/commit/ceab3db9d2a266616a0568e6359f33b9cd79aea6)

## [3.16.5](https://github.com/commenthol/date-holidays/compare/3.16.4...3.16.5) (2022-08-24)

- fix(DE-MV): International Women's Day [6479fc21](https://github.com/commenthol/date-holidays/commit/6479fc21a7f082370ead8607097957387b07a8ef)
- fix(geneva): remove jeune federal [af4d5640](https://github.com/commenthol/date-holidays/commit/af4d56408a18f7aa1a959e03e61a576348970534)

## [3.16.4](https://github.com/commenthol/date-holidays/compare/3.16.3...3.16.4) (2022-08-12)

- fix(translation): typo in French translation for 08-01 in CH.yaml [dad6d8b9](https://github.com/commenthol/date-holidays/commit/dad6d8b958dc7311527fdedd8682e5876cb6e9fa)

## [3.16.3](https://github.com/commenthol/date-holidays/compare/3.16.2...3.16.3) (2022-07-30)

- Add types to exports [bf244097](https://github.com/commenthol/date-holidays/commit/bf244097ff45d367338d9820647d062b9ffdf67d)

## [3.16.2](https://github.com/commenthol/date-holidays/compare/3.16.1...3.16.2) (2022-07-01)

- fix(AR,#343): Feriado Puente Turístico [046f615f](https://github.com/commenthol/date-holidays/commit/046f615faedb3586e6042fb9ac68810aa95def24)
- Fix NZ Matariki 2036 misprint [6522fd1b](https://github.com/commenthol/date-holidays/commit/6522fd1bf4dc8e2478ac2447890bddea9b3d6f37)

## [3.16.1](https://github.com/commenthol/date-holidays/compare/3.16.0...3.16.1) (2022-06-12)

- fix(US.CA): Presidents' Day name [845793aa](https://github.com/commenthol/date-holidays/commit/845793aa49c9a482ffbc5dacca144bef826c742a)

## [3.16.0](https://github.com/commenthol/date-holidays/compare/3.15.1...3.16.0) (2022-06-02)

- fix(SI): Re-verted regions placeholders [79f13907](https://github.com/commenthol/date-holidays/commit/79f13907fad14376858b9d2c2b1ca61fbf345211)
- feat(SI): Added Slovenian sport day [57f6033e](https://github.com/commenthol/date-holidays/commit/57f6033e9744f018602039d6f188ceacb55278a5)
- feat(SI): Udpated Slovenian Holidays and fixtures [64e636b2](https://github.com/commenthol/date-holidays/commit/64e636b2afa171c698099867200c9f15b28f343f)

## [3.15.0](https://github.com/commenthol/date-holidays/compare/3.14.10...3.15.0) (2022-05-21)

- docs: update attribution and README [88049ae2](https://github.com/commenthol/date-holidays/commit/88049ae2423519f4df07b4d7369fee8281de9914)
- fix(AT,#323): good friday is not a holiday [f613a85c](https://github.com/commenthol/date-holidays/commit/f613a85cd2d935e41c7105b51508cc280016c6d7)
- fix: macro support for nojalaali [608e8892](https://github.com/commenthol/date-holidays/commit/608e8892afabd8b6357d7f60cbb0b872a028e50a)
- fix: linter issue [c1ffcf65](https://github.com/commenthol/date-holidays/commit/c1ffcf6561a0822054795e57984e4785181f7810)
- docs: add persian calendar [b20d6f2c](https://github.com/commenthol/date-holidays/commit/b20d6f2c92e093a42c4babf84f3648924bd01575)
- feat(IR): new country Iran [e4005b33](https://github.com/commenthol/date-holidays/commit/e4005b33929fe8115d0d635ba7f4157e33910f2c)

## [3.14.10](https://github.com/commenthol/date-holidays/compare/3.14.9...3.14.10) (2022-04-23)

- fix(NO,#331): remove easter sat [b2e0a7f5](https://github.com/commenthol/date-holidays/commit/b2e0a7f5325769285663d42533a33499ce940040)

## [3.14.9](https://github.com/commenthol/date-holidays/compare/3.14.8...3.14.9) (2022-04-23)

- chore(ci): add node@18 [9a7fb227](https://github.com/commenthol/date-holidays/commit/9a7fb22797a50ffa354452da46e3ff7915d49d7c)
- fix(i18n): Fixes Work day spanish translation [176d6619](https://github.com/commenthol/date-holidays/commit/176d661952c86b13f282713632b0156c28aa8dbc)
- test(BN): Fix tests [2cdb4732](https://github.com/commenthol/date-holidays/commit/2cdb47324f0c90f90f9f6aa45ec7c1f7210459e7)
- fix(MY): Holidays corrections [dbebe0c9](https://github.com/commenthol/date-holidays/commit/dbebe0c950e316333f47bbd5b302284eee84ec4c)
- docs(MY): Add source [a70e19c4](https://github.com/commenthol/date-holidays/commit/a70e19c49fdc357269271e962faf312b74a2bbaf)
- fix(MY-02): missing Thaipusam day [776b4c58](https://github.com/commenthol/date-holidays/commit/776b4c58e5c675e3d8244fb2f2633413cef51f7b)

## [3.14.8](https://github.com/commenthol/date-holidays/compare/3.14.7...3.14.8) (2022-04-15)

- fix(BE): Mark Pentecost and Regional days as observance [fdf78fc1](https://github.com/commenthol/date-holidays/commit/fdf78fc1080de133e89d702f5f961240da147439)

## [3.14.7](https://github.com/commenthol/date-holidays/compare/3.14.6...3.14.7) (2022-04-14)

- fix: update SG Holidays [02f5229f](https://github.com/commenthol/date-holidays/commit/02f5229f52795dff15b5dfb618665c76c282bdf0)

## [3.14.6](https://github.com/commenthol/date-holidays/compare/3.14.5...3.14.6) (2022-04-09)

- test(RU,#320): fix tests for removal of flag day [19f87a10](https://github.com/commenthol/date-holidays/commit/19f87a10856ab2257982fd8ad0447734f2146816)
- Changes in public Ru holidays [07c3c9c6](https://github.com/commenthol/date-holidays/commit/07c3c9c6320c35a10f86e7d50b3a3f8efc0ce497)

## [3.14.5](https://github.com/commenthol/date-holidays/compare/3.14.4...3.14.5) (2022-04-09)

- fix(DE-HB,DE-HE,DE-TH,#321): Dec. 31 is not a public holiday [dd849428](https://github.com/commenthol/date-holidays/commit/dd849428249debf8472e8f65efc3b02ffb422452)

## [3.14.4](https://github.com/commenthol/date-holidays/compare/3.14.3...3.14.4) (2022-03-20)

- test(BE): fix tests [41b28b6c](https://github.com/commenthol/date-holidays/commit/41b28b6c93a1c9bed0a3faedfeacf1f8de3e950e)
- fix(BE): Easter isn't a paid holiday in belgium [255bf516](https://github.com/commenthol/date-holidays/commit/255bf516137307fbf1cbb322716553080679e902)

## [3.14.3](https://github.com/commenthol/date-holidays/compare/3.14.2...3.14.3) (2022-02-25)

- docs(CA): add reference for National Day for Truth and Reconciliation [ffb6c908](https://github.com/commenthol/date-holidays/commit/ffb6c9080a71966bcf3bf7231bcf407801515304)
- Update CA.yaml [33322664](https://github.com/commenthol/date-holidays/commit/3332266472bb4f85b955081e3d8f48bff3b76360)
- docs: update spec [32e05c32](https://github.com/commenthol/date-holidays/commit/32e05c326ada41f81f1d163e862824bdec0311c0)
- fix(HK): Use new if is holiday rule for day after mid-autumn festival [653bc7a9](https://github.com/commenthol/date-holidays/commit/653bc7a9b7af388833e16ec81d907b7448d8c00e)
- fix(NZ-OTA): Move Provincial anniversary day if on Easter Monday [95cbb0ff](https://github.com/commenthol/date-holidays/commit/95cbb0ff8fc02e7404750c858b6309f810916c1f)
- fix(CH-GL): Move Näfelser Fahrt if on Maundy Thursday [aaa171e4](https://github.com/commenthol/date-holidays/commit/aaa171e46e53e9de5db6e328ee343f44990661f0)
- fix(CH): Add Maundy Thursday as observance [a0e69bab](https://github.com/commenthol/date-holidays/commit/a0e69bab7a78f8bda036f13717b2c25a9aaa79f0)

## [3.14.2](https://github.com/commenthol/date-holidays/compare/3.14.1...3.14.2) (2022-01-12)

- fix(IL): join Mimouna with 7th day of Pesach [e85534c9](https://github.com/commenthol/date-holidays/commit/e85534c9d32182b05aa75af46b85870862d49fce)
- fix(IL): fix Mimouna date and add alternative names [17044cae](https://github.com/commenthol/date-holidays/commit/17044cae9444c54ab00e1e5acdea5259953e5751)
- chore(github-actions): enable push on master branch [302b2180](https://github.com/commenthol/date-holidays/commit/302b2180e9163a9e8d7c719dc5ccaa6d819c7c66)
- chore: use github actions [0b31c777](https://github.com/commenthol/date-holidays/commit/0b31c777248a03b04f10546ce141a01d89e170ce)

## [3.14.1](https://github.com/commenthol/date-holidays/compare/3.14.0...3.14.1) (2022-01-11)

- fix(SE): mark optional [889f0b9d](https://github.com/commenthol/date-holidays/commit/889f0b9d27ef2e417f51b750e4061b829032e2c7)
- fix(SE): holiday observances [73caefb1](https://github.com/commenthol/date-holidays/commit/73caefb18ea17c77f6cdb845de5ca5a1465add88)
- Update SE.yaml [a9766ccd](https://github.com/commenthol/date-holidays/commit/a9766ccd9903771caefcfbe651e06e19265c40c1)

## [3.14.0](https://github.com/commenthol/date-holidays/compare/3.13.1...3.14.0) (2022-01-04)

- fix(GB,GG,GI,IM,JE,#303): Spring bank holiday [59a33cec](https://github.com/commenthol/date-holidays/commit/59a33ceccf2b22dcd4b1f814614ef069d7472c54)
- feat(IL): New country Israel [e4dcca42](https://github.com/commenthol/date-holidays/commit/e4dcca4265aab77427226f9ba78ec103ea9cfe29)

## [3.13.1](https://github.com/commenthol/date-holidays/compare/3.13.0...3.13.1) (2021-12-30)

- fix(NZ,#288): Provincial anniversary days [7a2eaaf1](https://github.com/commenthol/date-holidays/commit/7a2eaaf160c4d30d6ac610cdff4e47f7148dd530)
- fix(NZ,#288): add Matariki and missing regions [cf431cc6](https://github.com/commenthol/date-holidays/commit/cf431cc65554f7e0f5a00528530d96ccbed4937a)

## [3.13.0](https://github.com/commenthol/date-holidays/compare/3.12.2...3.13.0) (2021-12-21)

- fix(GR: added easter 49 back as public holiday for Greece [6343368f](https://github.com/commenthol/date-holidays/commit/6343368f276ef70a8ce7fcf414c131a9730f31af)
- fix(GR,CY): changed easter 49 to easter 50 for Greece and updated name for Cyprus [ebb874a1](https://github.com/commenthol/date-holidays/commit/ebb874a1d94703826ec83eb536f06d5d682dc780)
- feat(AU): added Mothers day to au [8a039da2](https://github.com/commenthol/date-holidays/commit/8a039da22046c5bf4b5c3a6846424a89eaf895b0)
- feat(AU,GB,IE): fathers day for au, gb and ie [7b6d5edc](https://github.com/commenthol/date-holidays/commit/7b6d5edc5bf24d86c69aa26d0a17452db640c9e6)
- test(US): Saint Patrick's Day [63b7ed92](https://github.com/commenthol/date-holidays/commit/63b7ed92f34964b12e04c6489389bcd9c5bbdd04)
- fix(AS,GU,PR,VI): Saint Patrick's Day is not observed [f4ed7469](https://github.com/commenthol/date-holidays/commit/f4ed74697f7344f7f49e2577324b8519e6f3d342)
- feat(US): Add St. Patrick's Day for US [5252b7aa](https://github.com/commenthol/date-holidays/commit/5252b7aa9508ea09d9d0d28cfecd1f6152e6872e)

## [3.12.2](https://github.com/commenthol/date-holidays/compare/3.12.1...3.12.2) (2021-11-26)

- Move eslint-plugin-yml to devDependencies [53df5631](https://github.com/commenthol/date-holidays/commit/53df563173b1ef6073202f52180c0fa01d680f8b)

## [3.12.1](https://github.com/commenthol/date-holidays/compare/3.12.0...3.12.1) (2021-11-16)

- chore: yaml linter [e65ebeb6](https://github.com/commenthol/date-holidays/commit/e65ebeb65edefa1a22a62ec341bcd78d1f47eee1)
- fix(FJ): moment-timezone update [2b6d8093](https://github.com/commenthol/date-holidays/commit/2b6d8093e0b64a3d4efb8b1e6b8f0bb39b253d93)
- fix(ID): fixing Indonesia's independence day [3c372c91](https://github.com/commenthol/date-holidays/commit/3c372c91f37603943495f847b32fea574726761f)
- chore(travis): upgrade node version to 14, 16 and latest [08220cb2](https://github.com/commenthol/date-holidays/commit/08220cb2d198faea26f61f34f8e40c8490d64e6d)

## [3.12.0](https://github.com/commenthol/date-holidays/compare/3.11.0...3.12.0) (2021-10-29)

- docs: Readme and attribution update [5d87d9cd](https://github.com/commenthol/date-holidays/commit/5d87d9cdc22b3281eb4c1c726a5c9d30c38b76a4)
- fix(BN): holiday names in ms [b406e73b](https://github.com/commenthol/date-holidays/commit/b406e73b4cc6bbfc8c65fa7ce9ae27d99b44d823)
- feat: Add Malaysia holidays [a7e67180](https://github.com/commenthol/date-holidays/commit/a7e67180094a70753707b7c364688d3e3a553dab)

## [3.11.0](https://github.com/commenthol/date-holidays/compare/3.10.0...3.11.0) (2021-10-14)

- fix(NL,#285): change Bevrijdingsdag type to observance [7b546bd6](https://github.com/commenthol/date-holidays/commit/7b546bd661e4779c8410602ea17c529401f6a1c7)
- feat(NC): New country New Caledonia [8e14774f](https://github.com/commenthol/date-holidays/commit/8e14774f153d7b2692637299ecd44430a4f96956)
- Add New Caledonia [328355bf](https://github.com/commenthol/date-holidays/commit/328355bfa50094ef7f772465028db6bb5f8af48d)

## [3.10.0](https://github.com/commenthol/date-holidays/compare/3.9.1...3.10.0) (2021-10-07)

- chore: fix example [67148510](https://github.com/commenthol/date-holidays/commit/67148510f117b8b8defaa011e29132fdea44739c)
- fix(HU,#282): 12-06 change type to observance [3dad5f52](https://github.com/commenthol/date-holidays/commit/3dad5f52af016775b473fa4fd0856264d656b336)
- test(FI): added missing tests and some corrections [1e53646c](https://github.com/commenthol/date-holidays/commit/1e53646c586cd643db589f67bbad1fac9f47d0b0)
- feat(FI): added father's day to Finnish, corrected spelling of mother's day [1bd795db](https://github.com/commenthol/date-holidays/commit/1bd795dbf7ff59d292e95d438397ba58b41d1fab)

## [3.9.1](https://github.com/commenthol/date-holidays/compare/3.9.0...3.9.1) (2021-10-01)

- test(LU): fix failing tests [25a26894](https://github.com/commenthol/date-holidays/commit/25a26894b9d6d64a3439281276e8ab07c2e967e3)
- fix(LU): Remove incorrect public holiday [64df0cd5](https://github.com/commenthol/date-holidays/commit/64df0cd5588257d75130f3f79a45c56e4f00e119)

## [3.9.0](https://github.com/commenthol/date-holidays/compare/3.8.4...3.9.0) (2021-09-03)

- feat(EH): new country Western Sahara [e4c33909](https://github.com/commenthol/date-holidays/commit/e4c33909b25b405ed20e5df18df877b4830c0584)
- docs(ET): new holiday source [e9d0e897](https://github.com/commenthol/date-holidays/commit/e9d0e8979c948465e3a5571d379c5efb60aee0f5)
- feat(ER): new country Eritrea [7bcfdcc1](https://github.com/commenthol/date-holidays/commit/7bcfdcc1cda0196a07c77228f038ec1dddd91efc)
- feat(GH): new country Ghana [7715ecae](https://github.com/commenthol/date-holidays/commit/7715ecaed45c9aed58a53ed233abb510cfd24956)
- feat(GM): new country The Gambia [2677876f](https://github.com/commenthol/date-holidays/commit/2677876f90958faac9d780929751f2e348383e33)
- feat(GW): new country Guinea-Bissau [b247c960](https://github.com/commenthol/date-holidays/commit/b247c960f2227ffe04f468045f5c131147c7c576)
- feat(LR): new country Liberia [3bc00404](https://github.com/commenthol/date-holidays/commit/3bc0040485fc1e584b74ec19ba33fe964a4b7eb6)
- feat(LY): new coutry Libya [e3856800](https://github.com/commenthol/date-holidays/commit/e38568001494ac98cfd560d661c47297e1f62524)
- feat(MR): new country Mauretania [b47650d1](https://github.com/commenthol/date-holidays/commit/b47650d18de8de3923e72fb4c501e18b42addfb0)
- feat(NG): new country Nigeria [66b9749a](https://github.com/commenthol/date-holidays/commit/66b9749a1d5d4e6faea8c64bc68019e747c7f26a)
- feat(SD): new country Sudan [a58eacc2](https://github.com/commenthol/date-holidays/commit/a58eacc24b10bfab8a540e1afa71f8080ac8442d)
- feat(SL): new country Sierra Leone [9e5093a2](https://github.com/commenthol/date-holidays/commit/9e5093a230b6b17e1ef8e22f3b0b4bd26ed38c49)
- feat(ST): new country São Tomé & Príncipe [93cd290f](https://github.com/commenthol/date-holidays/commit/93cd290f5485ed5524e9b20d84360897c395f43d)
- feat(SZ): new country Eswatini [25e3a0fe](https://github.com/commenthol/date-holidays/commit/25e3a0fed13fadccaf5c70f0afd95b6650c031aa)
- feat(VG): new country British Virgin Islands [b57360c6](https://github.com/commenthol/date-holidays/commit/b57360c63900ec143ba680f493bf312bbde1b65d)
- feat(VI): new country U.S. Virgin Islands [48cf25b7](https://github.com/commenthol/date-holidays/commit/48cf25b7a510b1cc8ab64a0c17e39b7f33f72e1e)
- feat(VC): new country Saint Vincent and the Grenadines [71db0b3a](https://github.com/commenthol/date-holidays/commit/71db0b3aca7dc82ac71559374e56a379a109e195)
- feat(TT): new country Trinidad & Tobago [45b2b464](https://github.com/commenthol/date-holidays/commit/45b2b4645210b110635cd748bb9a65a480f92980)
- feat(TC): new country Turks & Caicos Islands [f7c81ec6](https://github.com/commenthol/date-holidays/commit/f7c81ec60a48b1733b679c0a6b046fd9c475ea4c)
- feat(SX): new country Sint Maarten [11899d42](https://github.com/commenthol/date-holidays/commit/11899d421d5bb67ad1841e910703b91559d5fbf9)
- feat(SR): New country Suriname [2d54a0d8](https://github.com/commenthol/date-holidays/commit/2d54a0d817fbb24436612c4f32c84e2c4e1af995)
- feat(PR): new country Puerto Rico [b9b44193](https://github.com/commenthol/date-holidays/commit/b9b44193a802a6aeddd07f3db75108cd32d46aa0)
- feat(PM): new country St. Pierre & Miquelon [217055b2](https://github.com/commenthol/date-holidays/commit/217055b26b811dab7731420a24ea8e4eb440fb14)
- feat(MS): new country Montserrat [fa50b54c](https://github.com/commenthol/date-holidays/commit/fa50b54cf5516a7204e79f9446d3edebd4881ecc)
- feat(LC): New country Saint Lucia [40d3807d](https://github.com/commenthol/date-holidays/commit/40d3807ddc02fe1a5c9cd600b7923599ab601021)

## [3.8.4](https://github.com/commenthol/date-holidays/compare/3.8.3...3.8.4) (2021-08-26)

- fix(CN): public holidays [f63c3f01](https://github.com/commenthol/date-holidays/commit/f63c3f018e86b738bb357af9807541bba9ae59bf)
- fix(FR): pentecost is not a public holiday [045b3019](https://github.com/commenthol/date-holidays/commit/045b3019e7c78e90b542268f21da804bd902df6a)
- test(example): pass on language [454f40f0](https://github.com/commenthol/date-holidays/commit/454f40f052d4984520592ba1b03296bcfbd0496e)

## [3.8.3](https://github.com/commenthol/date-holidays/compare/3.8.2...3.8.3) (2021-08-08)

- fix(GB,GG,GI,IM,JE): christmas substitution days [6e8d61f3](https://github.com/commenthol/date-holidays/commit/6e8d61f382ab39ef3216f7bc7526323f8abaa89c)
- fix(GB,GG,GI,IM,JE): Replace Early May with Spring bank holiday [36ac1c3c](https://github.com/commenthol/date-holidays/commit/36ac1c3cc85eb1ea1016e50f53e3ca185b3daf69)

## [3.8.2](https://github.com/commenthol/date-holidays/compare/3.8.1...3.8.2) (2021-08-08)

- fix(GB,GG,GI,IM,JE): Queen’s Platinum Jubilee [cd415972](https://github.com/commenthol/date-holidays/commit/cd4159725ac3e6c6ac9513f934c1d2e6c54d0189)
- chore: update travis badge [19f66753](https://github.com/commenthol/date-holidays/commit/19f667531de197210ae4bf35ec9c6da4470d5ab5)

## [3.8.1](https://github.com/commenthol/date-holidays/compare/3.8.0...3.8.1) (2021-08-03)

- test(AS,CH,EG,GU,IS,SE,US): fix test cases [6b4b6100](https://github.com/commenthol/date-holidays/commit/6b4b610058fd47e02b3d4b9a0b1ce573a1d83137)
- Fix some illegal dates [be9b045e](https://github.com/commenthol/date-holidays/commit/be9b045e5751a72258437e4f35f71c02a77b1df0)

## [3.8.0](https://github.com/commenthol/date-holidays/compare/3.7.0...3.8.0) (2021-06-29)

- feat(KY): Cayman Islands [e11f9f32](https://github.com/commenthol/date-holidays/commit/e11f9f328d6ff4334504c906450b4c678e5ee779)
- feat(KN): St. Kitts & Nevis [664fdc2c](https://github.com/commenthol/date-holidays/commit/664fdc2c3e1cc6ea0bfa27f4761fc282b329e749)
- feat(IC): Islas Canarias [dfa63530](https://github.com/commenthol/date-holidays/commit/dfa6353074a34fdfc0419286c2f0fb2c4c9bf9bd)
- Merge branch 'monkeydaichan-patch-1' [e4604add](https://github.com/commenthol/date-holidays/commit/e4604adde4f4432cff34d90115f6800e2b5abb3a)
- fix(PY): 2020 Día de la Paz del Chaco [3f78c839](https://github.com/commenthol/date-holidays/commit/3f78c839374b441fda55e271782c72ef7700a49b)
- Update PY.yaml [5a9c9438](https://github.com/commenthol/date-holidays/commit/5a9c943838c56b9b915a1be5511e3e49d73b58b3)

## [3.7.0](https://github.com/commenthol/date-holidays/compare/3.6.0...3.7.0) (2021-06-26)

- fix(TW): i18n [ef7375e8](https://github.com/commenthol/date-holidays/commit/ef7375e8bc58daafd3693bf44044b30f524bab11)
- fix(FJ,GY,SG): Vesak, Deepavali [9ed33178](https://github.com/commenthol/date-holidays/commit/9ed33178ddd6f2b6626da48833b83952898f9d79)
- fix(HK): i18n [7dff646e](https://github.com/commenthol/date-holidays/commit/7dff646ee69750f9cb715911844db6c3288fdee9)
- feat(TW): new country Taiwan [87a6cfd0](https://github.com/commenthol/date-holidays/commit/87a6cfd06ac215cd6195884ff12ebc12217b61cc)

## [3.6.0](https://github.com/commenthol/date-holidays/compare/3.5.1...3.6.0) (2021-06-19)

- fix(US): add Juneteenth ad public holiday [89c6db99](https://github.com/commenthol/date-holidays/commit/89c6db9978a224ef6b4c2cc21a2ca94a96831f86)
- fix(CH-GL): Näfelser Fahrt and other holidays [db764462](https://github.com/commenthol/date-holidays/commit/db764462b54fa4ae713289f9a764ce3ddbc8e5b8)
- feat(HK): New country HK [9238b082](https://github.com/commenthol/date-holidays/commit/9238b082ef12930237cb818d8e7926c1536e6826)

## [3.5.1](https://github.com/commenthol/date-holidays/compare/3.5.0...3.5.1) (2021-06-08)

- docs: update countries and attribution [cce1eb6c](https://github.com/commenthol/date-holidays/commit/cce1eb6ca684650fa64170aa9357507ddb710b57)
- fix(TN): official language order [2f402f35](https://github.com/commenthol/date-holidays/commit/2f402f35bce11651bffbb5b202e7fe99076c1ef6)
- fix(TD): official language order [bd216ef0](https://github.com/commenthol/date-holidays/commit/bd216ef0f19d6b97020c7860f34988eb5e198732)
- fix(SN): country name [f421a6a4](https://github.com/commenthol/date-holidays/commit/f421a6a433770dddd116acda85bec933d973e238)
- fix(SC): official language order [a704f2ac](https://github.com/commenthol/date-holidays/commit/a704f2ac20c9d83cb04d3200159e2e0d8a800c9f)
- fix(RW): official language order [ab25bab1](https://github.com/commenthol/date-holidays/commit/ab25bab15f56c35929abab487448b3dc1317dd04)
- fix(NE): add holiday names in en [382c92a1](https://github.com/commenthol/date-holidays/commit/382c92a1d71d92f4c8efb06d188f4c4259c79d14)
- fix(MQ): add holiday names in en [2501163d](https://github.com/commenthol/date-holidays/commit/2501163d26fea01606831f7bcf0bcf72b93f4849)
- fix(ML): add holiday names in en [e6901853](https://github.com/commenthol/date-holidays/commit/e6901853ced9c7db9b89a4248312de7411634c4a)
- fix(MA): add holiday names in en [374adf28](https://github.com/commenthol/date-holidays/commit/374adf28bae0dad398feac267f4f0b82d76112d3)
- fix(KM): add holiday names in en [17ff1bbd](https://github.com/commenthol/date-holidays/commit/17ff1bbd2c28bb8a8f7b4a2ca7bf79e23ddab9f3)
- fix(GP): add holiday names in en; attrib [e4b44693](https://github.com/commenthol/date-holidays/commit/e4b4469319dee4971fe3d6682c6b4e958b20a1ac)
- fix(GN): add holiday names in en [71c393d9](https://github.com/commenthol/date-holidays/commit/71c393d9c8f4eb96085dc60d904f7a7b21f503d6)
- fix(FR): add holiday names in en [57c09a9c](https://github.com/commenthol/date-holidays/commit/57c09a9c19c1ee8fff023c20d95ba6cdcdbb89a7)
- Merge branch 'master' into feat-MF [cb98c060](https://github.com/commenthol/date-holidays/commit/cb98c06016bb0a36502af272938ea8bfe6bdfe81)
- fix(MF): Saint Martin - update yaml MF:   - attachment to France   - update holiday - update test for MF and FR-MF [16ad1983](https://github.com/commenthol/date-holidays/commit/16ad19838f6a5d1188beab3f8035f0b85c4d23b6)
- Merge remote-tracking branch 'origin/fix-BL' into fix-BL [5984cbe7](https://github.com/commenthol/date-holidays/commit/5984cbe7f46f9879347de413394ffd38c54f2374)
- fix(BL): Saint Barthélemy - update yaml BL:   - attachment to France   - update holiday - update test for BL and FR-BL [67326d81](https://github.com/commenthol/date-holidays/commit/67326d81bb067b63ae2d17627c898ffa974a1d7b)
- fix(BL): Reunion - update yaml BL:   - attachment to France   - update holiday - update test for BL and FR-BL [287c5fc1](https://github.com/commenthol/date-holidays/commit/287c5fc12931bb6b0616f9d03579392c735fc730)
- fix(RE): Reunion - update yaml RE => Pentecost disabled - update test for RE [f2ccd986](https://github.com/commenthol/date-holidays/commit/f2ccd986b96dd2fca139ceaa2bbd25a674951707)
- Revert "fix(RE): Reunion - update yaml RE => Pentecost disabled - update test for RE" [a0cf4a6b](https://github.com/commenthol/date-holidays/commit/a0cf4a6b750add8ae282fef7461be0825f63d654)
- fix(RE): Reunion - update yaml RE => Pentecost disabled - update test for RE [a2fc2ddb](https://github.com/commenthol/date-holidays/commit/a2fc2ddba8f8c5bab94fd1c15d37e30ff667d073)
- fix(YT): Mayotte - update yaml YT - update test for YT [58518508](https://github.com/commenthol/date-holidays/commit/58518508b0ef19926435a9f37c1da2b8579b03b6)
- Merge remote-tracking branch 'origin/fix-MQ' into fix-MQ [5bc2044a](https://github.com/commenthol/date-holidays/commit/5bc2044ab09f4c25dd4f1399e490756e8f5bee24)
- fix(MQ): Martinique - update yaml MQ - update test for MQ [71f044c9](https://github.com/commenthol/date-holidays/commit/71f044c919861323151eb3f4512eb3e6a76050c8)
- fix(GF): French Guiana - update yaml MQ - update test for MQ [62978fdc](https://github.com/commenthol/date-holidays/commit/62978fdc20605e17330cbe7d0b5ce0ccb5ed53a7)
- Merge remote-tracking branch 'origin/Fix-GF' into Fix-GF [16934748](https://github.com/commenthol/date-holidays/commit/169347482ec4657a65b415b69ec28246544b8e43)
- fix(GF): French Guiana - update test [d5cebc10](https://github.com/commenthol/date-holidays/commit/d5cebc101e9bf20fdf2c8e35e2d93f569ea240f7)
- fix(GP) Guadeloupe - update test [ba817c35](https://github.com/commenthol/date-holidays/commit/ba817c35d4884496421e35903e813352bff9679b)
- fix(GP) Guadeloupe - Pentecost disabled [654ae361](https://github.com/commenthol/date-holidays/commit/654ae36152dd84607b0dc21a937e537f1a4d91fc)
- fix(GP) Guadeloupe - Add test and check [9bf0ff02](https://github.com/commenthol/date-holidays/commit/9bf0ff02525c4990cf9db31e72ea309cccf7bdf3)
- fix(GP) Guadeloupe - Add test and check [61b567d4](https://github.com/commenthol/date-holidays/commit/61b567d4737c79309ec72d9f5e97a3289705661c)
- fix(GP) Guadeloupe - Pentecost disabled - Add holiday for 07-21 [2bffc1a3](https://github.com/commenthol/date-holidays/commit/2bffc1a3b3a116ad9fd90c0408c19fca6ad5e03b)

## [3.5.0](https://github.com/commenthol/date-holidays/compare/3.4.0...3.5.0) (2021-05-22)

- fix: webpack exclude resources to decrease bundle sizes [9d7f0637](https://github.com/commenthol/date-holidays/commit/9d7f063767a1b21362ea614b21cffce0a06c6b12)
- fix: ts export namespace [604474a9](https://github.com/commenthol/date-holidays/commit/604474a9152fbcaf3aa6b97b150eec50e88cf620)  
  in your typescript project use

  ```ts
  import Holidays, { HolidaysTypes } from "date-holidays";
  ```

- fix: package.json module for angular [89b8b0bd](https://github.com/commenthol/date-holidays/commit/89b8b0bdc00c594e4c245701e5881b96b18bb75d)

## [3.4.0](https://github.com/commenthol/date-holidays/compare/3.3.0...3.4.0) (2021-05-16)

- feat(SC): New country Seychelles [1f6c1215](https://github.com/commenthol/date-holidays/commit/1f6c12152beb44501b26bbbd9c86aaf0dd1a991f)
- feat(VU): New country Vanuatu [eff90f8a](https://github.com/commenthol/date-holidays/commit/eff90f8a30a4f98ee46a61504d449dd547063817)
- feat(KM): New country Comoros [f1cac385](https://github.com/commenthol/date-holidays/commit/f1cac385bfe2c23b48fa6d708b9ac17210b334c5)
- fix(DJ): Add two holidays for Djibouti [7d0d8dbe](https://github.com/commenthol/date-holidays/commit/7d0d8dbe1ea31822d5a6988672bb909f32fa10d0)

## [3.3.0](https://github.com/commenthol/date-holidays/compare/3.2.0...3.3.0) (2021-05-08)

- fix(CF): Add two holidays for Central African Republic [fcd5b7c7](https://github.com/commenthol/date-holidays/commit/fcd5b7c7e7be69ec4e7c242b225f4a658a762c29)
- fix(TG): Add Ascension Day for Togo [6c1a70d8](https://github.com/commenthol/date-holidays/commit/6c1a70d8aa7ec302b1061db8c07bee5c66c46969)
- feat(TD): new country Chad [258c8378](https://github.com/commenthol/date-holidays/commit/258c837874fb2f8c83711ad3d210052e2110f3fe)
- feat(GN): new country Guinea [e9da83ca](https://github.com/commenthol/date-holidays/commit/e9da83ca180a4f73de2898e906ff02a57a45dd6f)
- feat(RW): Add holidays for country Rwanda [4d0cd383](https://github.com/commenthol/date-holidays/commit/4d0cd3835493b290f6b5572cf5e6fed2d9c05c10)
- feat(ML): Add holidays for country Mali [4b2994b7](https://github.com/commenthol/date-holidays/commit/4b2994b77b58fdad313da7e557bb3228d86c4c40)
- feat(SN): Add holidays for country Senegal [a3de7b72](https://github.com/commenthol/date-holidays/commit/a3de7b7201ddd6c6eea5a6cdd2b5b5d64074494b)
- feat(NE): Add holidays for country Niger [950fff26](https://github.com/commenthol/date-holidays/commit/950fff266475e573ee4525d37f0edaf0a4863f96)
- feat(MG): Add one holiday for country Madagascar [3f6804e9](https://github.com/commenthol/date-holidays/commit/3f6804e967a55f9613611c29d9298e50b40c5b5e)
- feat(MG): Add holidays for country Madagascar [26c2ba0b](https://github.com/commenthol/date-holidays/commit/26c2ba0bb500da95f98ec996b2e5df0a7446379c)
- feat(TN): new country Tunisia [73b3024d](https://github.com/commenthol/date-holidays/commit/73b3024deb6c37b08c74ee4500e7c07750cfb778)
- feat(MA): new country Morocco [2bae17bf](https://github.com/commenthol/date-holidays/commit/2bae17bf9dbc732b23da64bc904150b5323d12b8)

## [3.2.0](https://github.com/commenthol/date-holidays/compare/3.1.1...3.2.0) (2021-05-05)

- fix: typings [c319fd01](https://github.com/commenthol/date-holidays/commit/c319fd010a81f258d45bc3bb81d60150ba5fa21d)
- fix: export data [951fe813](https://github.com/commenthol/date-holidays/commit/951fe8137f3da07103d2d4fac046098c9874df3a)

## [3.1.1](https://github.com/commenthol/date-holidays/compare/3.1.0...3.1.1) (2021-04-14)

- fix(SE): Halloween has type observance [814674af](https://github.com/commenthol/date-holidays/commit/814674afa47b1a7286560265cdf0ec3c9acc9f42)

## [3.1.0](https://github.com/commenthol/date-holidays/compare/3.0.1...3.1.0) (2021-04-08)

- docs: update supported countries and license attribution [b4191511](https://github.com/commenthol/date-holidays/commit/b41915115fe0fd9a370b78cb8a59c79e66597bf8)
- fix(EG): default lang [e1f33161](https://github.com/commenthol/date-holidays/commit/e1f33161b50513a616d28abf4309b4b5de62bc37)
- test(GG,GI,IM,JE): Fix tests for channel islands [4a72b797](https://github.com/commenthol/date-holidays/commit/4a72b7970f4e8456babcf03b153b4601f32fdd42)
- test(AX): fix names [67e6ec61](https://github.com/commenthol/date-holidays/commit/67e6ec617bff888a82fa9a808571188205b13ba2)
- fix(GB): #203 boxing day substitution rules [a34aff0b](https://github.com/commenthol/date-holidays/commit/a34aff0bcaa7a47d80ec8a1411a96f34df7e4fd9)
- fix(SE): #165 missing evenings [a3338afa](https://github.com/commenthol/date-holidays/commit/a3338afa88a0ec8db549332b5eaf5b27ec160386)
- feat(FJ): new country fiji [8d7ebf73](https://github.com/commenthol/date-holidays/commit/8d7ebf73b2181beb5d32902ca0fe26704a98061e)
- feat(EG): new coutry egypt [21a748ff](https://github.com/commenthol/date-holidays/commit/21a748ff03fc4a066b8ee4aa3cb42582f364ec02)
- feat(DZ): new country algeria [30a7b693](https://github.com/commenthol/date-holidays/commit/30a7b693856c8dd9e8b8b6529f55e01fdd8be3da)
- feat(DJ): country djibouti [f73f02af](https://github.com/commenthol/date-holidays/commit/f73f02af84093eaa6dfc888f627e27f562dbc80a)
- feat(CI): add republic of côte d'ivoire [2f0fc207](https://github.com/commenthol/date-holidays/commit/2f0fc207d126c5b5e0af2e8375948534768ae34f)
- fix(TG): add country name [f76bf867](https://github.com/commenthol/date-holidays/commit/f76bf867221d08af2fd783564d875babab7607b7)
- feat(CK): cook islands [a683f980](https://github.com/commenthol/date-holidays/commit/a683f980e6d5f8918576d291db12e7613a2c85ba)
- fix(example): refactor [943bc058](https://github.com/commenthol/date-holidays/commit/943bc05829d0fc0ebceeb2662baaa49667af1d40)
- fix(example): hash route and language selection [a3629fc1](https://github.com/commenthol/date-holidays/commit/a3629fc1d4699aed9e8dd9c60462e14894cbd04d)
- fix: import [3db4d5b8](https://github.com/commenthol/date-holidays/commit/3db4d5b8b5d5798b5b7c25b29cb1e2510259124c)

## [3.0.1](https://github.com/commenthol/date-holidays/compare/3.0.0...3.0.1) (2021-03-27)

- fix(CA-QC): #200 Enable Easter Monday for Quebec [5dc08e4a](https://github.com/commenthol/date-holidays/commit/5dc08e4ab2759ea9aab994e51311ae0fc0a3cfe5)
- fix(CA-QC): #207 Disable Civic Holiday for Quebec [5400d586](https://github.com/commenthol/date-holidays/commit/5400d5868c56e82e44d2157d66fc555b8f42b6d5)

## [3.0.0](https://github.com/commenthol/date-holidays/compare/2.0.2...3.0.0) (2021-03-27)

- break: esm module [e81eb941](https://github.com/commenthol/date-holidays/commit/e81eb941a57bf0c402ddfbee6a70696980225073)

## [2.0.2](https://github.com/commenthol/date-holidays/compare/2.0.1...2.0.2) (2021-03-03)

- fix(EE): Update EE Independence Day to be a public holiday [5670ca72](https://github.com/commenthol/date-holidays/commit/5670ca72e00a5783160d566925eb3cf8dc7ee407)

## [2.0.1](https://github.com/commenthol/date-holidays/compare/2.0.0...2.0.1) (2021-03-01)

- fix(JP): revert regular Marine Day rule [86c81311](https://github.com/commenthol/date-holidays/commit/86c81311eb6717f247d6b1b9d85811a1b3c9b219)
- fix(JP): Add substitution rule for 07-20 so that 1997-07-21 is holiday [a0c210b3](https://github.com/commenthol/date-holidays/commit/a0c210b32c1ff4de253caaff97c51b7a26bf5b42)
- fix(JP): Add 4 public holidays 1959-04-10, 1989-02-24, 1990-11-12, 1993-06-09 [2b19c353](https://github.com/commenthol/date-holidays/commit/2b19c3539b9ee2968ac9e7fbc644eacf26383238)

## [2.0.0](https://github.com/commenthol/date-holidays/compare/1.9.1...2.0.0) (2021-02-13)

- chore(travis): remove node 10 [54986a6b](https://github.com/commenthol/date-holidays/commit/54986a6b59d57af1a1fe3b9c374919dac5e9e178)
- test: fix astronomia update [6d9d910f](https://github.com/commenthol/date-holidays/commit/6d9d910fdfa1c3cb61a9c07c470b8dbbc727997e)
- break: isHolidays() returns a list [7fe58d31](https://github.com/commenthol/date-holidays/commit/7fe58d31b6f277525e4560c8b87f4944e7b42017)

## [1.9.1](https://github.com/commenthol/date-holidays/compare/1.9.0...1.9.1) (2021-01-28)

- fix(#193): Adding "Europe Day" to Luxembourg's holidays [f58a4999](https://github.com/commenthol/date-holidays/commit/f58a4999f047a5cf711ad238ac5db556a9cc6268)
- fix(#193): Adding "Europe Day" to Luxembourg's holidays [44baf012](https://github.com/commenthol/date-holidays/commit/44baf0123236dec28d3bcc08b077e37402de60d1)

## [1.9.0](https://github.com/commenthol/date-holidays/compare/1.8.7...1.9.0) (2021-01-23)

- docs: update attribution [a86fe73c](https://github.com/commenthol/date-holidays/commit/a86fe73c29e4bb5edc5393c1efe052354fb02e49)
- docs: clarifying language about data aggregators [783a6462](https://github.com/commenthol/date-holidays/commit/783a646297c8c005691f0a083324fa3fc24b4afb)
- fix(US-GA): Fixes #142 GA Confederate Memorial Day 2020 [672261ce](https://github.com/commenthol/date-holidays/commit/672261cea8944b02fe7a16c6c6435fa9e9c1973f)
- docs: Guidelines for citations. [f32a5a49](https://github.com/commenthol/date-holidays/commit/f32a5a49a6c202bbc2c6704dcfb14cf66af6359b)
- feat(GE): Holidays for Georgia(Country) [70aae144](https://github.com/commenthol/date-holidays/commit/70aae144745bddbd1571b9623bb592ccb8d413f2)
- feat(GE): Holidays for Georgia(Country) [aa68e174](https://github.com/commenthol/date-holidays/commit/aa68e174da0dc16fbdf855f1dc05966d41e26351)

## [1.8.7](https://github.com/commenthol/date-holidays/compare/1.8.6...1.8.7) (2021-01-14)

- fix(CZ): Change Czech Easter Sunday type to "observance" [8c9ce16b](https://github.com/commenthol/date-holidays/commit/8c9ce16b386f16a4884fbe6c274535bd7c319fc9)

## [1.8.6](https://github.com/commenthol/date-holidays/compare/1.8.5...1.8.6) (2021-01-08)

- fix(JP): JP.yaml and tests [f897e3cf](https://github.com/commenthol/date-holidays/commit/f897e3cfc2fc2b5b1d08ada0e7ae3ab0da864aa2)

## [1.8.5](https://github.com/commenthol/date-holidays/compare/1.8.4...1.8.5) (2021-01-06)

- chore(webpack): analyze size [35d34cdb](https://github.com/commenthol/date-holidays/commit/35d34cdbe6a90c4c2dff472113eabb370d908345)
- fix(HU): corrected HU holidays and added translations [9ebc6e42](https://github.com/commenthol/date-holidays/commit/9ebc6e4200d0eb9a47cf6bea5edb5ef8c25f12a2)

## [1.8.4](https://github.com/commenthol/date-holidays/compare/1.8.3...1.8.4) (2020-12-31)

- Fix: [BR] Lovers' Day as observance [ea46204a](https://github.com/commenthol/date-holidays/commit/ea46204acf798d56b93ee824c2454dc979953820)

## [1.8.3](https://github.com/commenthol/date-holidays/compare/1.8.2...1.8.3) (2020-11-11)

- docs(ES): add official bulletin sources [691d1439](https://github.com/commenthol/date-holidays/commit/691d1439da2bf1b7dcfba70b760ca5f45cad88a2)
- fix: Add dependency for watch-run [3f84a3ac](https://github.com/commenthol/date-holidays/commit/3f84a3ac05eb20db9658591ca3c8473f84c09a80)
- fix(ES): Fix Spanish holidays according to official sources (BOE) [035ac167](https://github.com/commenthol/date-holidays/commit/035ac16739bd9a4b77d89bc037dc0dd1079b2825)

## [1.8.2](https://github.com/commenthol/date-holidays/compare/1.8.1...1.8.2) (2020-10-27)

- Add religious holidays [ac98830c](https://github.com/commenthol/date-holidays/commit/ac98830c70d2288040cf5ab3a9911c278ea7cb99)
- fix(HR): 03-08 name and new holidays name and active [03888aa1](https://github.com/commenthol/date-holidays/commit/03888aa1742cd6efebdc4e8739a75ec68c70a53c)
- fix(HR): Add missing type for HR 11-01 [c49cdcec](https://github.com/commenthol/date-holidays/commit/c49cdcec189bde8b00ba86c040aceb88189236b1)
- fix(HR): Update HR with 2020 change and add types [716acb7b](https://github.com/commenthol/date-holidays/commit/716acb7b45cadc0e3a515ee6667268be725636b6)

## [1.8.1](https://github.com/commenthol/date-holidays/compare/1.8.0...1.8.1) (2020-10-18)

- fix(NZ): ensure correct boxing day substitution [3ea68d6e](https://github.com/commenthol/date-holidays/commit/3ea68d6e23880c4dd1ded5ae0921cd2742fd401c)

## [1.8.0](https://github.com/commenthol/date-holidays/compare/1.7.1...1.8.0) (2020-10-12)

- feat(ID): added public holidays in Indonesia [a499debd](https://github.com/commenthol/date-holidays/commit/a499debd58c0d5999c0bffc333a212a710bfa442)

## [1.7.1](https://github.com/commenthol/date-holidays/compare/1.7.0...1.7.1) (2020-10-07)

- docs(AU): update source [131776f9](https://github.com/commenthol/date-holidays/commit/131776f9c0d871857fb92ccd879784f125de7664)
- fix(AU): exclude queen's birthday and labour day as national public holiday in AU [d6c26c5d](https://github.com/commenthol/date-holidays/commit/d6c26c5d95a8ea09da7c6015bf6c5b20caf70506)

## [1.7.0](https://github.com/commenthol/date-holidays/compare/1.6.2...1.7.0) (2020-09-27)

- fix(CA): Canada does not use US spelling of labour day [99818c5f](https://github.com/commenthol/date-holidays/commit/99818c5f9c6e2cdd7d2d014d80196abd070e69fb)
- feat(CA): add some Canadian observance type holidays [1d3b4eb9](https://github.com/commenthol/date-holidays/commit/1d3b4eb9b8e076e1c69e50a0d5206be41175511b)
- fix(CA): Add French translation for Fathers Day [76834039](https://github.com/commenthol/date-holidays/commit/768340392664bc08e40ca215a5e96fc91ca4f954)
- Update CA.yaml [84ac4a11](https://github.com/commenthol/date-holidays/commit/84ac4a11eb7cee97ed10af505ac654cc49734d13)

## [1.6.2](https://github.com/commenthol/date-holidays/compare/1.6.1...1.6.2) (2020-07-17)

- fix(SE.*): fix holidays for SE [36e451b6](https://github.com/commenthol/date-holidays/commit/36e451b61d49d80175c397748c58b85439a0db36)

## [1.6.1](https://github.com/commenthol/date-holidays/compare/1.6.0...1.6.1) (2020-06-24)

- fix(SE) Added 'midsommarafton' public holiday, old 'midsommar' -> 'midsommardagen' [76cc74a8](https://github.com/commenthol/date-holidays/commit/76cc74a84ab2fbf798a4622fc47ccb9991283eb7)

## [1.6.0](https://github.com/commenthol/date-holidays/compare/1.5.3...1.6.0) (2020-06-16)

- feat(LT) Add All Souls Day to Lithuania [a17dc03f](https://github.com/commenthol/date-holidays/commit/a17dc03f7aa0ea31314162a9c9b33cdfb551f6ac)
- test: reprocess tests [f0fb6179](https://github.com/commenthol/date-holidays/commit/f0fb61792ae07d4233d13eaf6e638117a68fd3f7)
- docs: add rule in output [7fa37f3c](https://github.com/commenthol/date-holidays/commit/7fa37f3cc42846f46acaf67c90b34b751e45547b)
- feat: add rule in Holidays object [6a0abe6d](https://github.com/commenthol/date-holidays/commit/6a0abe6d9d775dbfc6c6524233e74fb5c06de3f8)

## [1.5.3](https://github.com/commenthol/date-holidays/compare/1.5.2...1.5.3) (2020-06-06)

- fix(NL): Fixed holidays for NL [3bfa17ba](https://github.com/commenthol/date-holidays/commit/3bfa17ba921f43df03f92c40acec5ff868ed0136)

## [1.5.2](https://github.com/commenthol/date-holidays/compare/1.5.1...1.5.2) (2020-05-23)

- fix(RO): children's day is public [3ed93c2](https://github.com/commenthol/date-holidays/commit/3ed93c298668c327de24f074ea8d3ecc2cff6adf)

## [1.5.1](https://github.com/commenthol/date-holidays/compare/1.5.0...1.5.1) (2020-04-28)

- fix(CZ): new year, easter holiday names [a96ffb2](https://github.com/commenthol/date-holidays/commit/a96ffb2e88e00fd35d99b5a82cc90ff42b932946)
- test: TEST_XXL env runs all tests [678150f](https://github.com/commenthol/date-holidays/commit/678150f2f6b18b867ed59d6cc58d45ee181bbc43)
- test: add fixtures for years 2021-2025 [5fdbc24](https://github.com/commenthol/date-holidays/commit/5fdbc2451fbcf2b566e67500905487365bda4696)

## [1.5.0](https://github.com/commenthol/date-holidays/compare/1.4.14...1.5.0) (2020-04-18)

- feat(US): Easter Sunday observed in US [cd89982](https://github.com/commenthol/date-holidays/commit/cd899820dc16388023323aa35eb5c35843480d13)

## [1.4.14]https://github.com/commenthol/date-holidays/compare/1.4.13...1.4.14) (2020-03-13)

- fix(NZ): correct substitution of NZ holidays [0633cf9]

## [1.4.13](https://github.com/commenthol/date-holidays/compare/1.4.12...1.4.13) (2020-02-29)

- fix(DE): Changed "Faschingsdienstag" to observance [0e3cf5f]
- fix(AU.TAS): based on advice from WorkSafe Tasmania [8f21bbb]
- fix(AU): add back old fixtures [05cd81a]
- fix(AU): some fixtures, might have to delete old years [c0fa77e]
- fix(AU.TAS): checked against 2020-22 [1d9a71f]
- fix(AU.WA): checked for 2020-21 [1aad481]
- fix(AU.SA,AU.VIC): checked against 2020-2021 [696fa06]
- fix(AU.QLD): manually checked 2020-21 [7f8625f]
- refactor(AU.NT): remove dupe [0653e45]
- fix(AU.NT): manually checked 2019-20 [75a6d5f]
- fix(AU.NSW): manually checked 2020-2021 [bd62527]
- fix(AU.ACT): manually checked 2020-2022 [bdf7031]
- fix(AU.NT): partial public holidays [d612b04]
- fix(AU.ACT): fixed for 2020 [ed07b2c]
- docs(substitute): spelling mistake [683b0b9]
- fix(AU.QLD,AU.NT): add back partial christmas eve [4cae304]
- fix(AU): remove Christmas Eve [38085ca]

## [1.4.12](https://github.com/commenthol/date-holidays/compare/1.4.11...1.4.12) (2019-12-16)

- chore: commitlint disable scope-case [5950cf1](https://github.com/commenthol/date-holidays/commit/5950cf1d33dc4f4c0d9522a4abf27d5c577b7d11)
- fix(#129,DK): add observance holidays [a82ff24](https://github.com/commenthol/date-holidays/commit/a82ff243ebbca4b30bc1fea2666d01786f3b6ce8)
- fix(#133,AR): shrove monday [56ebcbe](https://github.com/commenthol/date-holidays/commit/56ebcbe34a52333a99784db655b3dfe889796dba)

## [1.4.11](https://github.com/commenthol/date-holidays/compare/1.4.10...1.4.11) (2019-12-14)

- chore: commitlint for semantic releasing [c8a848d](https://github.com/commenthol/date-holidays/commit/c8a848db4a2a115d7bf4a1c60ff574ea9976d00c)
- Fix typings for setLanguages function [8fa4f90](https://github.com/commenthol/date-holidays/commit/8fa4f90e4e8384ebaa988b4bbd114e12f50afddb)
- fix(#127): Fix Typo in Scotland [aaca6f5](https://github.com/commenthol/date-holidays/commit/aaca6f5dc713a295fd9a29baad9e9bda56b2a9a0)
- fix(AU-SA): New Year's Eve [6f7c670](https://github.com/commenthol/date-holidays/commit/6f7c670328357062f102285dcc575173f14bd5b8)

## [1.4.10](https://github.com/commenthol/date-holidays/compare/v1.4.9...v1.4.10) (2019-12-06)

- fix(CA-BC, #124): Family day on 3rd Monday
  Family Day in CA-BC shifts from 2nd Monday in February to 3rd starting 2019
- fix(typescript, #121): Reflected change of isHoliday to class

## [1.4.9](https://github.com/commenthol/date-holidays/compare/v1.4.8...v1.4.9) (2019-11-22)

- fix(GB, GG, IM, JE, GI): Early May bank holiday (VE day) on May 8th 2020
- fix(DE-BE): Liberation Day 75th Anniversary

## [1.4.8](https://github.com/commenthol/date-holidays/compare/v1.4.7...v1.4.8) (2019-11-17)

- fix(typescript, #117): isHoliday return type

## [1.4.7](https://github.com/commenthol/date-holidays/compare/v1.4.6...v1.4.7) (2019-11-05)

- fix(NZ-CAN): Set Christchurch Show Day to 2nd Friday after 1st Tuesday

## [1.4.6](https://github.com/commenthol/date-holidays/compare/v1.4.5...v1.4.6) (2019-11-02)

- c0b5082 chore(typescript): fix ts typings

## [1.4.5](https://github.com/commenthol/date-holidays/compare/v1.4.4...v1.4.5) (2019-10-12)

- e4443d4 fix(US): CA Labor Day

## [1.4.4](https://github.com/commenthol/date-holidays/compare/v1.4.3...v1.4.4) (2019-10-09)

- 3ad8d07 chore: bump deps

## [1.4.3](https://github.com/commenthol/date-holidays/compare/v1.4.2...v1.4.3) (2019-09-19)

- 96a7c99 Dates for Catalonia State of Spain

## [1.4.2](https://github.com/commenthol/date-holidays/compare/v1.4.1...v1.4.2) (2019-08-05)

- aaf326c chore: bump deps
- f0aad3d fix: Remembrance day in Netherlands
- a22dbcd Update NL.yaml
- 52104d7 Fix Vietnamese (naming convention)
- 2ca3138 Update VN.yaml

## [1.4.1](https://github.com/commenthol/date-holidays/compare/v1.4.0...v1.4.1) (2019-07-13)

- 8c43335 fix(ts): TypeScript fix for HolidaysConstructor
- b6c7953 fix(KE): Fix typo in Kenya
- b5aad5a fix(ts): TypeScript fixes for Holiday and HolidaysInterface

## [1.4.0](https://github.com/commenthol/date-holidays/compare/v1.3.11...v1.4.0) (2019-04-14)

- 2077ce0 feat(BD): new country Bangladesh
- d4f1310 feat: add support for bengali-revised calendar
- d34b6b8 fix(US): correct indigenous peoples day

## [1.3.10](https://github.com/commenthol/date-holidays/compare/v1.3.9...v1.3.10) (2019-03-31)

- 3792105 fix(doc): typo
- 9abbd26 fix(example): weekdays not shown for some dates
- 1b76a97 fix(JP): substitue days; dates; new days

## [1.3.9](https://github.com/commenthol/date-holidays/compare/v1.3.8...v1.3.9) (2019-03-30)

- 194cc3a fix(GU): fix Guam holidays
- 50f39a4 fix(US-CA): Update Hawaiian state holidays
- 720cb1c fix(US-CA): Update California state holidays
- b327792 fix(US-AK): Renaming Columbus Day to Indigenous Peoples Day
- 8832cdb feat(US): Veteran's Day Federal Offices closure
- c9dde21 fix(US-MA): fix timezone
- 1ddcf0e fix(CH): adding dayoff attribute
- 0595893 feat(DE-TH): New holiday Intl. Children's Day
- c3d0ab5 fix(US-MO): Add Lincoln's Birthday
- eca1216 fix(US-HI): Add Presidents' Day in Hawaii

## [1.3.8](https://github.com/commenthol/date-holidays/compare/v1.3.7...v1.3.8) (2019-03-04)

- bb38450 test(KR): failing test due to deltat update in astronomia
- 6b3c82d fix(example): use local date for weekday
- b361f0c fix(US): Different names for Washington's Birthday
- 9d290c6 feat(SG): New Country Singapore
- ece44e7 fix(NO): Frigjøringsdagen type observance

## [1.3.7](https://github.com/commenthol/date-holidays/compare/v1.3.6...v1.3.7) (2019-02-13)

- DE: Reformation Day for DE-HB DE-HH DE-NI DE-SH and International Women's Day for DE-BE
- new maintainer Ryan Gerry

## [1.3.6](https://github.com/commenthol/date-holidays/compare/v1.3.5...v1.3.6) (2019-02-03)

- RO: Add Unification Day and Orthodox Good Friday
- git: ignore compiled holidays.json

## [1.3.5](https://github.com/commenthol/date-holidays/compare/v1.3.4...v1.3.5) (2019-01-23)

- Typescript: Interface export
- DE: misspelling corrected "Baden Würtemberg" -> "Baden-Württemberg"

## [1.3.4](https://github.com/commenthol/date-holidays/compare/v1.3.3...v1.3.4) (2019-01-15)

- US: Modify New Year's Eve in the US to be an all day holiday.
- IE: Adding another banking source for IE.
- IE: adding Christmas time bank holidays.
- IE: adding a substitute bank holiday for St. Patrick's and two refinements

## [1.3.3](https://github.com/commenthol/date-holidays/compare/v1.3.2...v1.3.3) (2019-01-07)

- HR: Fix easter holidays
- PH: Adding optional and observed holidays
- AU-QLD: Fix holidays
- doc: Update spec with "Renaming holidays" section
- MX: Adding bank holidays
- PY: Update Hero's Day and 2017 holidays
- KR: Fix test

## [1.3.2](https://github.com/commenthol/date-holidays/compare/v1.3.1...v1.3.2) (2018-12-27)

- Add dist bundles for unpkg CDN

## [1.3.1](https://github.com/commenthol/date-holidays/compare/v1.3.0...v1.3.1) (2018-12-27)

- Fix attribution typo for Vatican City.
- Fix CH holiday names in default language
- Fix CH holidays in different cantons
- Fix CH All Swiss cantons with their names in en, de, fr, it

## [1.3.0](https://github.com/commenthol/date-holidays/compare/v1.2.10...v1.3.0) (2018-12-22)

- New weekday rule

## [1.2.10](https://github.com/commenthol/date-holidays/compare/v1.2.9...v1.2.10) (2018-12-13)

- Fix: Add Summer bank holiday to GB-NIR.

## [1.2.9](https://github.com/commenthol/date-holidays/compare/v1.2.8...v1.2.9) (2018-12-05)

- Update docs
- New Country Phillipines
- New country Brunei
- New country Bermuda
- New country Benin
- New country Bahrain
- New country Burkina Faso
- New country United Arab Emirates
- DE: Add Advent sundays
- New country Christmas Island
- New country Cape Verde
- Fix name "Assunção de Maria" for "pt"

## [1.2.8](https://github.com/commenthol/date-holidays/compare/v1.2.7...v1.2.8) (2018-08-29)

- Fix name "Assunção de Maria" for "pt"
- New country Cape Verde
- New country Christmas Island
- Add observed Advent Sundays to DE

## [1.2.7](https://github.com/commenthol/date-holidays/compare/v1.2.6...v1.2.7) (2018-08-27)

- Fix Norwegian holiday names
- New country St. Helena
- New country Lesotho
- New country Tonga

## [1.2.6](https://github.com/commenthol/date-holidays/compare/v1.2.5...v1.2.6) (2018-07-28)

- New holiday Turkey Democracy and National Unity Day

## [1.2.5](https://github.com/commenthol/date-holidays/compare/v1.2.4...v1.2.5) (2018-04-14)

- keep bundle sizes small

## [1.2.4](https://github.com/commenthol/date-holidays/compare/v1.2.2...v1.2.4) (2018-04-14)

- removing module, jsnext:main exports
- bundle example with webpack

## [1.2.2](https://github.com/commenthol/date-holidays/compare/v1.2.1...v1.2.2) (2018-02-11)

- New states: Brasil
- fix BR-SP Revolução Constitucionalista to 9th July

## [1.2.1](https://github.com/commenthol/date-holidays/compare/v1.2.0...v1.2.1) (2018-02-07)

- New regions: Brasil

## [1.2.0](https://github.com/commenthol/date-holidays/compare/v1.1.1...v1.2.0) (2017-11-04)

- Parser is moved into new repo [date-holidays-parser](https://github.com/commenthol/date-holidays-parser)
- Minimizing data files and build requires `--min` on `holidays2json` script
- New Countries
  - Greenland
  - Domenica
  - Curacao
  - Vatikan City
  - Ukraine
  - Slovenia
  - Serbia
  - Moldavia
  - Slovakia
  - Svalbard & Jan Mayen
  - San Marino
  - Macedonia
  - Kosovo
  - Gibraltar
- Moving French oversee departments to own Country-Code
- Fix Bosnia and Herzegovina orthodox christmas and names

## [1.1.1](https://github.com/commenthol/date-holidays/compare/v1.1.0...v1.1.1) (2017-11-03)

- Adding ES Andalucía, Andalucía

## [1.1.0](https://github.com/commenthol/date-holidays/compare/v1.0.1...v1.1.0) (2017-09-10)

- fix DK: Danish lang code
- fix DE: adding Berlin as state See #27
- Adding new Countries: AL, GY, FO, BA, CC
- Adding attribution marker `@attrib`
- Add country files \*.todo which require holiday data
- refactor `active` property for rules to en-/ disable rule in time periods

## [1.0.0](https://github.com/commenthol/date-holidays/compare/v0.2.1...v1.0.0) (2017-06-15)

- major refactoring
- Using ISO 8601 format for time spans
- Renaming divisions, subdivisions to match [CLDR](http://www.unicode.org/cldr/charts/30/supplemental/territory_subdivisions.html)
- Adding new countries: AG, AI, AS, AW, AX, AZ, BB, BL, BQ, BY, CN, GG, IM, JE, KR, SO, VN

## [0.2.0](https://github.com/commenthol/date-holidays/compare/v0.1.10...v0.2.0) (2017-06-07)

- split each country into a single yaml file
- DE: Correction of holidays in BY, SN, TH
  - DE-BB: fix easter, pentecoste to "public"
  - DE-BY: use 08-15 as public in favor of population majority
  - Set 10-31 to observance
  - Add missing "Fronleichnam" to regions to DE-SN and DE-TH
  - Fix DE-BY holiday „Mariä Himmelfahrt“
  - Add missing school DE-BW holidays
  - Add missing DE Holiday on 2017-10-31
  - Fix DE holiday „Buß- und Bettag“
- DE: Add Totensonntag, Volkstrauertag

## [0.1.10](https://github.com/commenthol/date-holidays/compare/v0.1.9...v0.1.10) (2017-05-03)

- fix for US New Years Eve
- fix for Labor Day spelling in en-us countries
- new rule for optional Christmas Eve in US

## [0.1.9](https://github.com/commenthol/date-holidays/compare/v0.1.8...v0.1.9) (2017-04-27)

- custom builds with holidays2json

## [0.1.8](https://github.com/commenthol/date-holidays/compare/v0.1.7...v0.1.8) (2017-03-21)

- missing holidays AT

## [0.1.7](https://github.com/commenthol/date-holidays/compare/v0.1.6...v0.1.7) (2017-02-15)

- new holidays for CA states/ provinces

## [0.1.6](https://github.com/commenthol/date-holidays/compare/v0.1.5...v0.1.6) (2017-01-19)

- fix AU.nsw bank holidays

## [0.1.5](https://github.com/commenthol/date-holidays/compare/v0.1.4...v0.1.5) (2016-04-18)

- Country Angola added
- Country Mozambique added
- Country Botswana added
- Country Namibia added
- Country South Africa added
- Country Bahamas added
- Country Dominican Republic added
- Country Haiti added
- Country Jamaica added

## [0.1.4](https://github.com/commenthol/date-holidays/compare/v0.1.3...v0.1.4) (2016-01-31)

- New general query function used in `date-holidays-ical`

## [0.1.3](https://github.com/commenthol/date-holidays/compare/v0.1.2...v0.1.3) (2016-01-09)

- Country New Zealand added
- Country Cuba added
- Guatemala: Día de las Fuerzas Armadas added
- Country Belize added
- Country Guatemala added
- Country El Salvador added
- Country Honduras added

## [0.1.2](https://github.com/commenthol/date-holidays/compare/v0.1.1...v0.1.2) (2016-01-04)

- Country Nicaragua added
- Country Costa Rica added
- Country Panama added
- Country Grenada added

## [0.1.1](https://github.com/commenthol/date-holidays/compare/v0.1.0...v0.1.1) (2016-01-04)

- Country Colombia added
- Country Venezuela added
- Country Uruguay added
- Country Ecuador added
- Country Peru added
- Country Paraguay added
- Country Bolivia added
- Country Chile added
