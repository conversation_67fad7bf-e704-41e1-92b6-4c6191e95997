const mysql = require('mysql2/promise');

async function checkDuplicateTitles() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Find duplicate titles in holidays
    console.log('\n🔍 Checking for duplicate holiday titles...');
    const [duplicates] = await connection.execute(`
      SELECT 
        title,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id ORDER BY calendar_id) as ids,
        GROUP_CONCAT(DATE_FORMAT(event_date, '%Y-%m-%d') ORDER BY calendar_id) as dates,
        GROUP_CONCAT(category_id ORDER BY calendar_id) as categories
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title
      HAVING COUNT(*) > 1
      ORDER BY count DESC, title
    `);
    
    if (duplicates.length === 0) {
      console.log('✅ No duplicate holiday titles found!');
    } else {
      console.log(`⚠️ Found ${duplicates.length} holiday titles with duplicates:`);
      
      duplicates.forEach((dup, index) => {
        const ids = dup.ids.split(',');
        const dates = dup.dates.split(',');
        const categories = dup.categories.split(',');
        
        console.log(`\n${index + 1}. "${dup.title}" - ${dup.count} copies:`);
        for (let i = 0; i < ids.length; i++) {
          console.log(`   - ID: ${ids[i]}, Date: ${dates[i]}, Category: ${categories[i]}`);
        }
      });
      
      // Show total impact
      const totalDuplicateRecords = duplicates.reduce((sum, dup) => sum + (dup.count - 1), 0);
      console.log(`\n📊 Duplicate Impact:`);
      console.log(`- Duplicate title sets: ${duplicates.length}`);
      console.log(`- Extra duplicate records: ${totalDuplicateRecords}`);
      console.log(`- Records that would remain after cleanup: ${duplicates.length}`);
    }
    
    // Show overall statistics
    const [totalHolidays] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [uniqueTitles] = await connection.execute('SELECT COUNT(DISTINCT title) as count FROM school_calendar WHERE is_holiday = 1');
    
    console.log(`\n📊 Overall Statistics:`);
    console.log(`- Total holiday records: ${totalHolidays[0].count}`);
    console.log(`- Unique holiday titles: ${uniqueTitles[0].count}`);
    
    if (totalHolidays[0].count === uniqueTitles[0].count) {
      console.log('✅ Perfect! Each holiday title appears exactly once.');
    } else {
      console.log(`⚠️ There are ${totalHolidays[0].count - uniqueTitles[0].count} duplicate records.`);
    }
    
    // Check for same title + same year (actual problematic duplicates)
    console.log('\n🔍 Checking for ACTUAL problematic duplicates (same title + same year)...');
    const [actualDuplicates] = await connection.execute(`
      SELECT 
        title,
        YEAR(event_date) as year,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id ORDER BY calendar_id) as ids
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title, YEAR(event_date)
      HAVING COUNT(*) > 1
      ORDER BY count DESC, title
    `);
    
    if (actualDuplicates.length === 0) {
      console.log('✅ No actual problematic duplicates found!');
      console.log('   (Each holiday title appears only once per year - this is correct)');
    } else {
      console.log(`❌ Found ${actualDuplicates.length} ACTUAL problematic duplicates:`);
      actualDuplicates.forEach(dup => {
        console.log(`- "${dup.title}" in ${dup.year}: ${dup.count} copies (IDs: ${dup.ids})`);
      });
    }
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Duplicate check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkDuplicateTitles();
