const holidayService = require('./src/services/holidayService');

async function fullHolidaySync() {
  console.log('Full Holiday Sync for 2024 and 2025...\n');

  try {
    // Sync holidays for 2024
    console.log('=== Syncing holidays for 2024 ===');
    const syncResults2024 = await holidayService.syncHolidaysToDatabase(2024, 1);
    
    console.log('2024 Sync Results:');
    console.log(`- Created: ${syncResults2024.created} holidays`);
    console.log(`- Updated: ${syncResults2024.updated} holidays`);
    console.log(`- Skipped: ${syncResults2024.skipped} holidays`);
    console.log(`- Errors: ${syncResults2024.errors.length} errors`);

    // Sync holidays for 2025
    console.log('\n=== Syncing holidays for 2025 ===');
    const syncResults2025 = await holidayService.syncHolidaysToDatabase(2025, 1);
    
    console.log('2025 Sync Results:');
    console.log(`- Created: ${syncResults2025.created} holidays`);
    console.log(`- Updated: ${syncResults2025.updated} holidays`);
    console.log(`- Skipped: ${syncResults2025.skipped} holidays`);
    console.log(`- Errors: ${syncResults2025.errors.length} errors`);

    // Get final statistics
    console.log('\n=== Final Statistics ===');
    const holidays2024 = await holidayService.getHolidaysFromDatabase(2024);
    const holidays2025 = await holidayService.getHolidaysFromDatabase(2025);
    
    console.log(`Total holidays in database:`);
    console.log(`- 2024: ${holidays2024.length} holidays`);
    console.log(`- 2025: ${holidays2025.length} holidays`);

    // Group by category for 2024
    const byCategory2024 = {};
    holidays2024.forEach(holiday => {
      const category = holiday.category_name || 'Unknown';
      byCategory2024[category] = (byCategory2024[category] || 0) + 1;
    });
    
    console.log('\n2024 Holidays by category:');
    Object.entries(byCategory2024).forEach(([category, count]) => {
      console.log(`- ${category}: ${count} holidays`);
    });

    // Show sample holidays
    console.log('\n=== Sample 2024 holidays ===');
    holidays2024.slice(0, 10).forEach(holiday => {
      console.log(`- ${holiday.title} (${holiday.event_date.toISOString().split('T')[0]}) [${holiday.category_name}]`);
    });

    console.log('\nHoliday sync completed successfully!');

  } catch (error) {
    console.error('Error in full holiday sync:', error);
  }
}

fullHolidaySync();
