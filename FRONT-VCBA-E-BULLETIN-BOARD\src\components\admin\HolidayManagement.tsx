import React, { useState, useEffect } from 'react';
import { holidayService, Holiday, HolidayStats, SyncResults } from '../../services/holidayService';
import { Calendar, RefreshCw, Download, Upload, Trash2, Eye, BarChart3, Globe, MapPin, Star } from 'lucide-react';

interface HolidayManagementProps {
  onClose?: () => void;
}

const HolidayManagement: React.FC<HolidayManagementProps> = ({ onClose }) => {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [stats, setStats] = useState<HolidayStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'philippine' | 'international' | 'religious' | 'sync'>('overview');
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Load holidays and stats
  const loadData = async () => {
    setLoading(true);
    try {
      const [holidaysData, statsData] = await Promise.all([
        holidayService.getHolidays(currentYear),
        holidayService.getHolidayStats(currentYear)
      ]);
      setHolidays(holidaysData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading holiday data:', error);
      setErrorMessage('Failed to load holiday data');
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts or year changes
  useEffect(() => {
    loadData();
  }, [currentYear]);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (successMessage || errorMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, errorMessage]);

  // Sync holidays
  const handleSync = async (force = false) => {
    setSyncing(true);
    try {
      const results = await holidayService.syncHolidays(currentYear, force);
      setSuccessMessage(
        `Sync completed! Created: ${results.created}, Updated: ${results.updated}, Skipped: ${results.skipped}`
      );
      await loadData(); // Reload data after sync
    } catch (error) {
      console.error('Error syncing holidays:', error);
      setErrorMessage('Failed to sync holidays');
    } finally {
      setSyncing(false);
    }
  };

  // Delete auto-generated holidays
  const handleDeleteAutoGenerated = async () => {
    if (!window.confirm(`Are you sure you want to delete all auto-generated holidays for ${currentYear}?`)) {
      return;
    }

    setLoading(true);
    try {
      const deletedCount = await holidayService.deleteAutoGeneratedHolidays(currentYear);
      setSuccessMessage(`Deleted ${deletedCount} auto-generated holidays`);
      await loadData(); // Reload data after deletion
    } catch (error) {
      console.error('Error deleting holidays:', error);
      setErrorMessage('Failed to delete holidays');
    } finally {
      setLoading(false);
    }
  };

  // Filter holidays by category
  const getHolidaysByCategory = (category: string) => {
    return holidays.filter(holiday => {
      switch (category) {
        case 'philippine':
          return holiday.holiday_type === 'local' && holiday.country_code === 'PH';
        case 'international':
          return holiday.holiday_type === 'international' || holiday.is_global;
        case 'religious':
          return holiday.category_name?.toLowerCase().includes('religious');
        default:
          return true;
      }
    });
  };

  // Render holiday list
  const renderHolidayList = (holidayList: Holiday[]) => (
    <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
      {holidayList.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '2rem', 
          color: '#6b7280' 
        }}>
          No holidays found for this category
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          {holidayList.map((holiday) => (
            <div
              key={holiday.calendar_id}
              style={{
                padding: '1rem',
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                borderLeft: `4px solid ${holiday.category_color}`,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <div style={{ flex: 1 }}>
                <div style={{ 
                  fontWeight: '600', 
                  color: '#111827',
                  marginBottom: '0.25rem'
                }}>
                  {holiday.title}
                </div>
                <div style={{ 
                  fontSize: '0.875rem', 
                  color: '#6b7280',
                  marginBottom: '0.25rem'
                }}>
                  {new Date(holiday.event_date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
                <div style={{ 
                  fontSize: '0.75rem', 
                  color: '#9ca3af',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <span style={{
                    backgroundColor: holiday.category_color,
                    color: 'white',
                    padding: '0.125rem 0.375rem',
                    borderRadius: '4px'
                  }}>
                    {holiday.category_name}
                  </span>
                  {holiday.country_code && (
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <MapPin size={12} />
                      {holiday.country_code}
                    </span>
                  )}
                  {holiday.is_auto_generated && (
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <RefreshCw size={12} />
                      Auto-generated
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        width: '100%',
        maxWidth: '1200px',
        maxHeight: '90vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <Calendar style={{ color: '#22c55e' }} size={24} />
            <h2 style={{ 
              margin: 0, 
              fontSize: '1.5rem', 
              fontWeight: '600', 
              color: '#111827' 
            }}>
              Holiday Management
            </h2>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <select
              value={currentYear}
              onChange={(e) => setCurrentYear(parseInt(e.target.value))}
              style={{
                padding: '0.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '0.875rem'
              }}
            >
              {Array.from({ length: 6 }, (_, i) => new Date().getFullYear() - 1 + i).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
            {onClose && (
              <button
                onClick={onClose}
                style={{
                  padding: '0.5rem',
                  backgroundColor: '#f3f4f6',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1.25rem',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            )}
          </div>
        </div>

        {/* Messages */}
        {(successMessage || errorMessage) && (
          <div style={{
            padding: '1rem 1.5rem',
            backgroundColor: successMessage ? '#f0fdf4' : '#fef2f2',
            borderBottom: '1px solid #e5e7eb',
            color: successMessage ? '#166534' : '#dc2626'
          }}>
            {successMessage || errorMessage}
          </div>
        )}

        {/* Tab Navigation */}
        <div style={{
          padding: '0 1.5rem',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          gap: '0.5rem'
        }}>
          {[
            { key: 'overview', label: 'Overview', icon: BarChart3 },
            { key: 'philippine', label: 'Philippine', icon: MapPin },
            { key: 'international', label: 'International', icon: Globe },
            { key: 'religious', label: 'Religious', icon: Star },
            { key: 'sync', label: 'Sync', icon: RefreshCw }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              style={{
                padding: '0.75rem 1rem',
                border: 'none',
                backgroundColor: 'transparent',
                borderBottom: activeTab === key ? '2px solid #22c55e' : '2px solid transparent',
                color: activeTab === key ? '#22c55e' : '#6b7280',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
            >
              <Icon size={16} />
              {label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          padding: '1.5rem',
          overflow: 'auto'
        }}>
          {loading ? (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '200px' 
            }}>
              <RefreshCw className="animate-spin" size={24} style={{ color: '#22c55e' }} />
            </div>
          ) : (
            <>
              {activeTab === 'overview' && stats && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  {/* Stats Cards */}
                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                    gap: '1rem' 
                  }}>
                    <div style={{
                      padding: '1.5rem',
                      backgroundColor: '#f0fdf4',
                      borderRadius: '8px',
                      border: '1px solid #bbf7d0'
                    }}>
                      <div style={{ fontSize: '2rem', fontWeight: '700', color: '#166534' }}>
                        {stats.total}
                      </div>
                      <div style={{ fontSize: '0.875rem', color: '#166534' }}>
                        Total Holidays
                      </div>
                    </div>
                    <div style={{
                      padding: '1.5rem',
                      backgroundColor: '#eff6ff',
                      borderRadius: '8px',
                      border: '1px solid #bfdbfe'
                    }}>
                      <div style={{ fontSize: '2rem', fontWeight: '700', color: '#1d4ed8' }}>
                        {stats.autoGenerated}
                      </div>
                      <div style={{ fontSize: '0.875rem', color: '#1d4ed8' }}>
                        Auto-Generated
                      </div>
                    </div>
                    <div style={{
                      padding: '1.5rem',
                      backgroundColor: '#fef3c7',
                      borderRadius: '8px',
                      border: '1px solid #fde68a'
                    }}>
                      <div style={{ fontSize: '2rem', fontWeight: '700', color: '#92400e' }}>
                        {stats.manual}
                      </div>
                      <div style={{ fontSize: '0.875rem', color: '#92400e' }}>
                        Manual
                      </div>
                    </div>
                  </div>

                  {/* Category Breakdown */}
                  <div>
                    <h3 style={{ margin: '0 0 1rem 0', fontSize: '1.125rem', fontWeight: '600' }}>
                      By Category
                    </h3>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      {Object.entries(stats.byCategory).map(([category, count]) => (
                        <div key={category} style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.75rem',
                          backgroundColor: '#f9fafb',
                          borderRadius: '6px'
                        }}>
                          <span style={{ fontWeight: '500' }}>{category}</span>
                          <span style={{ 
                            backgroundColor: '#22c55e',
                            color: 'white',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '4px',
                            fontSize: '0.875rem'
                          }}>
                            {count}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'philippine' && renderHolidayList(getHolidaysByCategory('philippine'))}
              {activeTab === 'international' && renderHolidayList(getHolidaysByCategory('international'))}
              {activeTab === 'religious' && renderHolidayList(getHolidaysByCategory('religious'))}

              {activeTab === 'sync' && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  <div>
                    <h3 style={{ margin: '0 0 1rem 0', fontSize: '1.125rem', fontWeight: '600' }}>
                      Sync Holidays
                    </h3>
                    <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
                      Sync holidays from external sources to the database. This will automatically 
                      create holiday events for Philippine holidays, international holidays, and religious observances.
                    </p>
                    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
                      <button
                        onClick={() => handleSync(false)}
                        disabled={syncing}
                        style={{
                          padding: '0.75rem 1.5rem',
                          backgroundColor: '#22c55e',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: syncing ? 'not-allowed' : 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          opacity: syncing ? 0.6 : 1
                        }}
                      >
                        {syncing ? <RefreshCw className="animate-spin" size={16} /> : <Download size={16} />}
                        {syncing ? 'Syncing...' : 'Sync Holidays'}
                      </button>
                      <button
                        onClick={() => handleSync(true)}
                        disabled={syncing}
                        style={{
                          padding: '0.75rem 1.5rem',
                          backgroundColor: '#f59e0b',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: syncing ? 'not-allowed' : 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          opacity: syncing ? 0.6 : 1
                        }}
                      >
                        <Upload size={16} />
                        Force Sync
                      </button>
                      <button
                        onClick={handleDeleteAutoGenerated}
                        disabled={loading}
                        style={{
                          padding: '0.75rem 1.5rem',
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: loading ? 'not-allowed' : 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          fontSize: '0.875rem',
                          fontWeight: '500',
                          opacity: loading ? 0.6 : 1
                        }}
                      >
                        <Trash2 size={16} />
                        Delete Auto-Generated
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HolidayManagement;
