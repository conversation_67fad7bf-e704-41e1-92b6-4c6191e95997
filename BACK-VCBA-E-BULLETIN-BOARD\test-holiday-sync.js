const holidayService = require('./src/services/holidayService');

async function testHolidaySync() {
  console.log('Testing Holiday Sync to Database...\n');

  try {
    // Test syncing holidays for 2024
    console.log('=== Syncing holidays for 2024 ===');
    const syncResults = await holidayService.syncHolidaysToDatabase(2024, 1);
    
    console.log('Sync Results:');
    console.log(`- Created: ${syncResults.created} holidays`);
    console.log(`- Updated: ${syncResults.updated} holidays`);
    console.log(`- Skipped: ${syncResults.skipped} holidays`);
    console.log(`- Errors: ${syncResults.errors.length} errors`);
    
    if (syncResults.errors.length > 0) {
      console.log('\nErrors:');
      syncResults.errors.forEach(error => {
        console.log(`- ${error.holiday}: ${error.error}`);
      });
    }

    // Test getting holidays from database
    console.log('\n=== Getting holidays from database ===');
    const dbHolidays = await holidayService.getHolidaysFromDatabase(2024);
    console.log(`Found ${dbHolidays.length} holidays in database for 2024`);

    // Group by category
    const byCategory = {};
    dbHolidays.forEach(holiday => {
      const category = holiday.category_name || 'Unknown';
      byCategory[category] = (byCategory[category] || 0) + 1;
    });
    
    console.log('\nHolidays by category in database:');
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`- ${category}: ${count} holidays`);
    });

    // Show sample holidays from database
    console.log('\n=== Sample holidays from database ===');
    const sampleHolidays = dbHolidays.slice(0, 10);
    sampleHolidays.forEach(holiday => {
      console.log(`- ${holiday.title} (${holiday.event_date}) [${holiday.category_name || 'No Category'}] - ${holiday.holiday_type}`);
    });

    // Test filtering by category
    console.log('\n=== Testing category filtering ===');
    const philippineOnly = await holidayService.getHolidaysFromDatabase(2024, { category: 'philippine' });
    console.log(`Philippine holidays: ${philippineOnly.length}`);
    
    const internationalOnly = await holidayService.getHolidaysFromDatabase(2024, { category: 'international' });
    console.log(`International holidays: ${internationalOnly.length}`);

    // Test holiday statistics
    console.log('\n=== Holiday Statistics ===');
    const stats = {
      total: dbHolidays.length,
      autoGenerated: dbHolidays.filter(h => h.is_auto_generated).length,
      manual: dbHolidays.filter(h => !h.is_auto_generated).length,
      byMonth: {}
    };

    dbHolidays.forEach(holiday => {
      const month = new Date(holiday.event_date).getMonth() + 1;
      stats.byMonth[month] = (stats.byMonth[month] || 0) + 1;
    });

    console.log(`Total holidays: ${stats.total}`);
    console.log(`Auto-generated: ${stats.autoGenerated}`);
    console.log(`Manual: ${stats.manual}`);
    
    console.log('\nHolidays by month:');
    Object.keys(stats.byMonth).sort((a, b) => a - b).forEach(month => {
      const monthName = new Date(2024, month - 1, 1).toLocaleString('default', { month: 'long' });
      console.log(`- ${monthName}: ${stats.byMonth[month]} holidays`);
    });

  } catch (error) {
    console.error('Error testing holiday sync:', error);
  }
}

testHolidaySync();
