{"name": "prepin", "version": "1.0.3", "description": "An in-place pre-processor for javascript files using C style pre-compile syntax to compose custom builds from npm-packages", "keywords": ["builds", "custom", "inplace", "preprocessor"], "homepage": "https://github.com/commenthol/prepin#readme", "bugs": {"url": "https://github.com/commenthol/prepin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/commenthol/prepin.git"}, "license": "Unlicense", "author": "commenthol <<EMAIL>>", "main": "lib", "jsnext:main": "src", "bin": {"prepin": "./bin/prepin.js"}, "directories": {"lib": "lib", "example": "example", "test": "test"}, "scripts": {"all": "npm-run-all clean lint build test", "build": "babel -d lib src", "clean": "<PERSON><PERSON><PERSON> lib", "lint": "eslint src test", "prepublishOnly": "npm run all", "test": "mocha"}, "babel": {"presets": ["env"]}, "eslintConfig": {"extends": "standard", "plugins": ["standard"]}, "dependencies": {}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-preset-env": "^1.7.0", "babel-register": "^6.26.0", "eslint": "^5.16.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-node": "^9.0.1", "eslint-plugin-promise": "^4.1.1", "eslint-plugin-standard": "^4.0.0", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "rimraf": "^2.6.3"}}