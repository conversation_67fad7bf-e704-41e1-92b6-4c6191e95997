const mysql = require('mysql2/promise');
const holidayService = require('./src/services/holidayService');

async function createFreshApiHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Check ID 39 first
    const [id39] = await connection.execute('SELECT * FROM school_calendar WHERE calendar_id = 39');
    if (id39.length > 0) {
      console.log(`✅ ID 39 exists: "${id39[0].title}" (${id39[0].event_date}) - Will be preserved`);
    } else {
      console.log('⚠️ ID 39 does not exist');
    }
    
    // Count current holidays
    const [current] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Current holidays: ${current[0].count}`);
    
    // Delete all holidays except ID 39
    console.log('🗑️ Deleting all holidays except ID 39...');
    const [result] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${result.affectedRows} holiday records`);
    
    // Verify deletion
    const [after] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Holidays remaining: ${after[0].count}`);
    
    await connection.end();
    console.log('✅ Database connection closed after cleanup');
    
    // Now sync fresh holidays from API
    console.log('\n🔄 Syncing fresh holidays from API...');
    
    // Define the years to sync
    const currentYear = new Date().getFullYear();
    const yearsToSync = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1, currentYear + 2]; // 5 years total
    
    console.log(`📅 Syncing holidays for years: ${yearsToSync.join(', ')}`);
    
    let totalCreated = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    
    for (const year of yearsToSync) {
      console.log(`\n=== Syncing holidays for ${year} ===`);
      
      try {
        // Use the holiday service to sync holidays for this year
        const syncResults = await holidayService.syncHolidaysToDatabase(year, 1); // Admin ID 1
        
        console.log(`${year} Results:`);
        console.log(`- Created: ${syncResults.created} holidays`);
        console.log(`- Updated: ${syncResults.updated} holidays`);
        console.log(`- Skipped: ${syncResults.skipped} holidays`);
        console.log(`- Errors: ${syncResults.errors.length} errors`);
        
        totalCreated += syncResults.created;
        totalUpdated += syncResults.updated;
        totalErrors += syncResults.errors.length;
        
        if (syncResults.errors.length > 0) {
          console.log('Errors:');
          syncResults.errors.forEach(error => {
            console.log(`  - ${error.holiday}: ${error.error}`);
          });
        }
      } catch (error) {
        console.error(`❌ Error syncing ${year}:`, error.message);
        totalErrors++;
      }
    }
    
    // Final verification
    console.log('\n=== Final Results ===');
    
    const finalConnection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    const [finalCount] = await finalConnection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Total holidays in database: ${finalCount[0].count}`);
    
    // Show holidays by year
    const [byYear] = await finalConnection.execute(`
      SELECT YEAR(event_date) as year, COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY YEAR(event_date)
      ORDER BY year
    `);
    
    console.log('\n📅 Holidays by year:');
    byYear.forEach(item => {
      console.log(`- ${item.year}: ${item.count} holidays`);
    });
    
    // Show sample holidays
    console.log('\n=== Sample holidays ===');
    const [samples] = await finalConnection.execute(`
      SELECT calendar_id, title, event_date, category_name, is_auto_generated
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 10
    `);
    
    samples.forEach(holiday => {
      const autoGen = holiday.is_auto_generated ? '(auto)' : '(manual)';
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) [${holiday.category_name}] ${autoGen}`);
    });
    
    await finalConnection.end();
    
    console.log('\n🎉 Fresh API holiday sync completed!');
    console.log(`📊 Summary:`);
    console.log(`- Total created: ${totalCreated}`);
    console.log(`- Total updated: ${totalUpdated}`);
    console.log(`- Total errors: ${totalErrors}`);
    console.log(`- Years synced: ${yearsToSync.length}`);
    console.log('\n💡 The calendar should now display fresh holiday data from the API!');
    
  } catch (error) {
    console.error('❌ Error in fresh API holiday sync:', error.message);
    console.error(error.stack);
  }
}

createFreshApiHolidays();
