const holidayService = require('./src/services/holidayService');

async function syncAllYearsHolidays() {
  console.log('🎉 Syncing holidays for multiple years to display them all the time...\n');

  try {
    // Define the range of years to sync (current year ± 5 years for comprehensive coverage)
    const currentYear = new Date().getFullYear();
    const yearsToSync = [];
    
    // Add years from 5 years ago to 5 years in the future
    for (let i = -5; i <= 5; i++) {
      yearsToSync.push(currentYear + i);
    }

    console.log(`📅 Years to sync: ${yearsToSync.join(', ')}`);
    console.log(`📊 Total years: ${yearsToSync.length} years\n`);

    // Sync holidays for each year
    const allResults = {};
    let totalCreated = 0;
    let totalUpdated = 0;
    let totalSkipped = 0;
    let totalErrors = 0;

    for (const year of yearsToSync) {
      try {
        console.log(`\n=== Syncing holidays for ${year} ===`);
        const syncResults = await holidayService.syncHolidaysToDatabase(year, 1); // Use admin ID 1
        
        allResults[year] = syncResults;
        totalCreated += syncResults.created;
        totalUpdated += syncResults.updated;
        totalSkipped += syncResults.skipped;
        totalErrors += syncResults.errors.length;

        console.log(`${year} Results:`);
        console.log(`- Created: ${syncResults.created} holidays`);
        console.log(`- Updated: ${syncResults.updated} holidays`);
        console.log(`- Skipped: ${syncResults.skipped} holidays`);
        console.log(`- Errors: ${syncResults.errors.length} errors`);

        if (syncResults.errors.length > 0) {
          console.log('Errors:');
          syncResults.errors.forEach(error => {
            console.log(`  - ${error.holiday}: ${error.error}`);
          });
        }

      } catch (error) {
        console.error(`❌ Error syncing ${year}:`, error.message);
        allResults[year] = { created: 0, updated: 0, skipped: 0, errors: [{ holiday: 'Sync failed', error: error.message }] };
        totalErrors++;
      }
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎊 HOLIDAY SYNC SUMMARY');
    console.log('='.repeat(60));
    console.log(`📅 Years synced: ${yearsToSync.length}`);
    console.log(`✅ Total created: ${totalCreated} holidays`);
    console.log(`🔄 Total updated: ${totalUpdated} holidays`);
    console.log(`⏭️  Total skipped: ${totalSkipped} holidays`);
    console.log(`❌ Total errors: ${totalErrors} errors`);

    // Show breakdown by year
    console.log('\n📊 Breakdown by year:');
    yearsToSync.forEach(year => {
      const result = allResults[year];
      if (result) {
        const total = result.created + result.updated + result.skipped;
        console.log(`- ${year}: ${total} holidays (${result.created} new, ${result.updated} updated, ${result.skipped} skipped)`);
      }
    });

    // Verify the results
    console.log('\n=== Verification ===');
    const holidays2024 = await holidayService.getHolidaysFromDatabase(2024);
    const holidays2025 = await holidayService.getHolidaysFromDatabase(2025);
    const holidays2026 = await holidayService.getHolidaysFromDatabase(2026);
    
    console.log(`✅ 2024: ${holidays2024.length} holidays in database`);
    console.log(`✅ 2025: ${holidays2025.length} holidays in database`);
    console.log(`✅ 2026: ${holidays2026.length} holidays in database`);

    // Show sample holidays from different years
    console.log('\n=== Sample holidays from different years ===');
    
    if (holidays2024.length > 0) {
      console.log('\n2024 holidays (first 5):');
      holidays2024.slice(0, 5).forEach(holiday => {
        console.log(`- ${holiday.event_date}: ${holiday.title} [${holiday.category_name}]`);
      });
    }

    if (holidays2025.length > 0) {
      console.log('\n2025 holidays (first 5):');
      holidays2025.slice(0, 5).forEach(holiday => {
        console.log(`- ${holiday.event_date}: ${holiday.title} [${holiday.category_name}]`);
      });
    }

    if (holidays2026.length > 0) {
      console.log('\n2026 holidays (first 5):');
      holidays2026.slice(0, 5).forEach(holiday => {
        console.log(`- ${holiday.event_date}: ${holiday.title} [${holiday.category_name}]`);
      });
    }

    console.log('\n🎉 Holiday sync completed! Holidays should now be visible across all years.');
    console.log('🗓️  You can now navigate through different months and years and see holidays consistently.');

  } catch (error) {
    console.error('💥 Error in holiday sync:', error);
  }
}

syncAllYearsHolidays();
