'use strict';

var CalDate = require('caldate');
var CalEvent = require('./CalEvent.cjs');
var jalaali = require('jalaali-js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var CalDate__default = /*#__PURE__*/_interopDefaultLegacy(CalDate);
var jalaali__default = /*#__PURE__*/_interopDefaultLegacy(jalaali);

const { toJalaali, toGregorian } = jalaali__default["default"];

class Jalaali extends CalEvent {
  /**
   * @param {number} year gregorian year
   * @returns
   */
  inYear (year) {
    const nowruz = toJalaali(year, 1, 1);

    if (this.opts.year && nowruz.jy !== this.opts.year) {
      return this
    }
    const { gy, gm, gd } = toGregorian(this.opts.year || nowruz.jy, this.opts.month, this.opts.day);

    const d = (new CalDate__default["default"]({ year: gy, month: gm, day: gd })).setOffset(this.offset);
    this.dates.push(d);
    return this
  }
}

module.exports = Jalaali;
