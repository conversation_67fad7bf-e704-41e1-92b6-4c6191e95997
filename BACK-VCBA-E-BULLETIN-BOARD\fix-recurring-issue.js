const mysql = require('mysql2/promise');

async function fixHolidayRecurringIssue() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Check current holidays with recurring settings
    console.log('🔍 Checking holidays with recurring settings...');
    const [recurringHolidays] = await connection.execute(`
      SELECT calendar_id, title, is_recurring, recurrence_pattern, event_date
      FROM school_calendar 
      WHERE is_holiday = 1 AND is_recurring = 1
      ORDER BY event_date
      LIMIT 10
    `);
    
    console.log(`Found ${recurringHolidays.length} holidays with recurring settings:`);
    recurringHolidays.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}", Recurring: ${holiday.is_recurring}, Pattern: ${holiday.recurrence_pattern}`);
    });
    
    // Update all holidays to remove recurring settings since the system doesn't support them
    console.log('\n🔧 Removing recurring settings from all holidays...');
    const [updateResult] = await connection.execute(`
      UPDATE school_calendar 
      SET is_recurring = 0, recurrence_pattern = NULL 
      WHERE is_holiday = 1
    `);
    
    console.log(`✅ Updated ${updateResult.affectedRows} holiday records to remove recurring settings`);
    
    // Verify the update
    const [afterUpdate] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM school_calendar 
      WHERE is_holiday = 1 AND is_recurring = 1
    `);
    
    console.log(`📊 Holidays with recurring settings after update: ${afterUpdate[0].count}`);
    
    // Show sample updated holidays
    console.log('\n📋 Sample updated holidays:');
    const [sampleUpdated] = await connection.execute(`
      SELECT calendar_id, title, is_recurring, recurrence_pattern, event_date
      FROM school_calendar 
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 5
    `);
    
    sampleUpdated.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}", Recurring: ${holiday.is_recurring}, Pattern: ${holiday.recurrence_pattern || 'NULL'}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Holiday recurring settings fixed!');
    console.log('💡 Now try editing holidays - they should work without the recurring validation issues');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

fixHolidayRecurringIssue();
