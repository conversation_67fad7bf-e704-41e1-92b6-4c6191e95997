const mysql = require('mysql2/promise');

async function removeAndCreateCompleteHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Remove all holidays except ID 39
    console.log('🗑️ Removing existing holidays (except ID 39)...');
    const [deleteResult] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${deleteResult.affectedRows} holiday records`);
    
    // Get categories
    const [categories] = await connection.execute('SELECT * FROM categories WHERE name LIKE "%Holiday%" ORDER BY category_id');
    const philippineCategoryId = categories.find(cat => cat.name.includes('Philippine'))?.category_id || 8;
    const internationalCategoryId = categories.find(cat => cat.name.includes('International'))?.category_id || 9;
    const religiousCategoryId = categories.find(cat => cat.name.includes('Religious'))?.category_id || 10;
    
    console.log('📋 Using categories:');
    console.log(`- Philippine Holidays: ID ${philippineCategoryId}`);
    console.log(`- International Holidays: ID ${internationalCategoryId}`);
    console.log(`- Religious Holidays: ID ${religiousCategoryId}`);
    
    const currentYear = new Date().getFullYear();
    
    // Complete Philippine Holidays
    const philippineHolidays = [
      { name: "New Year's Day", date: '01-01', description: 'First day of the year' },
      { name: 'People Power Anniversary', date: '02-25', description: 'EDSA Revolution Anniversary' },
      { name: 'Day of Valor', date: '04-09', description: 'Araw ng Kagitingan - Bataan and Corregidor Day' },
      { name: 'Maundy Thursday', date: '04-17', description: 'Holy Thursday before Easter' },
      { name: 'Good Friday', date: '04-18', description: 'Friday before Easter Sunday' },
      { name: 'Black Saturday', date: '04-19', description: 'Saturday before Easter Sunday' },
      { name: 'Labor Day', date: '05-01', description: "International Workers' Day" },
      { name: 'Independence Day', date: '06-12', description: 'Philippine Independence from Spain' },
      { name: 'Ninoy Aquino Day', date: '08-21', description: 'Assassination of Benigno Aquino Jr.' },
      { name: 'National Heroes Day', date: '08-25', description: 'Last Monday of August - Heroes Day' },
      { name: "All Saints' Day", date: '11-01', description: 'Day to honor all saints' },
      { name: "All Souls' Day", date: '11-02', description: 'Day of the Dead' },
      { name: 'Bonifacio Day', date: '11-30', description: 'Birthday of Andres Bonifacio' },
      { name: 'Immaculate Conception', date: '12-08', description: 'Feast of the Immaculate Conception' },
      { name: 'Christmas Eve', date: '12-24', description: 'Day before Christmas' },
      { name: 'Christmas Day', date: '12-25', description: 'Birth of Jesus Christ' },
      { name: 'Rizal Day', date: '12-30', description: 'Death anniversary of Dr. Jose Rizal' },
      { name: "New Year's Eve", date: '12-31', description: 'Last day of the year' }
    ];
    
    // International Holidays
    const internationalHolidays = [
      { name: "Valentine's Day", date: '02-14', description: 'Day of love and romance' },
      { name: "International Women's Day", date: '03-08', description: "Celebrates women's achievements worldwide" },
      { name: 'Earth Day', date: '04-22', description: 'Environmental awareness day' },
      { name: "Mother's Day", date: '05-12', description: 'Second Sunday of May - Honor mothers' },
      { name: "Father's Day", date: '06-15', description: 'Third Sunday of June - Honor fathers' },
      { name: 'World Environment Day', date: '06-05', description: 'UN World Environment Day' },
      { name: 'Halloween', date: '10-31', description: "All Hallows' Eve" },
      { name: 'Thanksgiving', date: '11-28', description: 'Fourth Thursday of November (US)' }
    ];
    
    // Religious Holidays (Various religions)
    const religiousHolidays = [
      { name: 'Chinese New Year', date: '01-29', description: 'Lunar New Year celebration' },
      { name: 'Ash Wednesday', date: '03-05', description: 'Beginning of Lent' },
      { name: 'Palm Sunday', date: '04-13', description: 'Sunday before Easter' },
      { name: 'Easter Sunday', date: '04-20', description: 'Resurrection of Jesus Christ' },
      { name: 'Eid al-Fitr', date: '04-10', description: 'End of Ramadan' },
      { name: 'Eid al-Adha', date: '06-16', description: 'Festival of Sacrifice' },
      { name: 'Diwali', date: '10-20', description: 'Hindu Festival of Lights' },
      { name: 'Hanukkah', date: '12-25', description: 'Jewish Festival of Lights (8 days)' }
    ];
    
    let totalCreated = 0;
    
    // Create Philippine holidays
    console.log('\n🇵🇭 Creating Philippine holidays...');
    for (const holiday of philippineHolidays) {
      const eventDate = `${currentYear}-${holiday.date}`;
      
      const [result] = await connection.execute(`
        INSERT INTO school_calendar (
          title, description, event_date, category_id,
          is_recurring, recurrence_pattern, is_active, is_published,
          allow_comments, is_alert, is_holiday, holiday_type,
          country_code, is_auto_generated, api_source, created_by,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'local', 'PH', 1, 'manual', 1, NOW(), NOW())
      `, [holiday.name, holiday.description, eventDate, philippineCategoryId]);
      
      console.log(`✅ ${holiday.name} (ID: ${result.insertId})`);
      totalCreated++;
    }
    
    // Create International holidays
    console.log('\n🌍 Creating International holidays...');
    for (const holiday of internationalHolidays) {
      const eventDate = `${currentYear}-${holiday.date}`;
      
      const [result] = await connection.execute(`
        INSERT INTO school_calendar (
          title, description, event_date, category_id,
          is_recurring, recurrence_pattern, is_active, is_published,
          allow_comments, is_alert, is_holiday, holiday_type,
          country_code, is_auto_generated, api_source, created_by,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'international', NULL, 1, 'manual', 1, NOW(), NOW())
      `, [holiday.name, holiday.description, eventDate, internationalCategoryId]);
      
      console.log(`✅ ${holiday.name} (ID: ${result.insertId})`);
      totalCreated++;
    }
    
    // Create Religious holidays
    console.log('\n🕊️ Creating Religious holidays...');
    for (const holiday of religiousHolidays) {
      const eventDate = `${currentYear}-${holiday.date}`;
      
      const [result] = await connection.execute(`
        INSERT INTO school_calendar (
          title, description, event_date, category_id,
          is_recurring, recurrence_pattern, is_active, is_published,
          allow_comments, is_alert, is_holiday, holiday_type,
          country_code, is_auto_generated, api_source, created_by,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'religious', NULL, 1, 'manual', 1, NOW(), NOW())
      `, [holiday.name, holiday.description, eventDate, religiousCategoryId]);
      
      console.log(`✅ ${holiday.name} (ID: ${result.insertId})`);
      totalCreated++;
    }
    
    // Final verification
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [recurringCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1 AND is_recurring = 1');
    
    console.log('\n📊 Final Results:');
    console.log(`- Philippine holidays: ${philippineHolidays.length}`);
    console.log(`- International holidays: ${internationalHolidays.length}`);
    console.log(`- Religious holidays: ${religiousHolidays.length}`);
    console.log(`- Total holidays created: ${totalCreated}`);
    console.log(`- Total holidays in database: ${finalCount[0].count}`);
    console.log(`- Recurring holidays: ${recurringCount[0].count}`);
    
    // Show holidays by category
    console.log('\n📋 Holidays by category:');
    const [byCategory] = await connection.execute(`
      SELECT c.name as category_name, COUNT(*) as count
      FROM school_calendar sc
      JOIN categories c ON sc.category_id = c.category_id
      WHERE sc.is_holiday = 1
      GROUP BY c.name
      ORDER BY count DESC
    `);
    
    byCategory.forEach(cat => {
      console.log(`- ${cat.category_name}: ${cat.count} holidays`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Complete holiday set created successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeAndCreateCompleteHolidays();
