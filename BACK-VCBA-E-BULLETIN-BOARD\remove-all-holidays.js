const mysql = require('mysql2/promise');

async function removeAllHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Check ID 39 first
    const [id39Check] = await connection.execute('SELECT * FROM school_calendar WHERE calendar_id = 39');
    if (id39Check.length > 0) {
      console.log(`✅ ID 39 exists: "${id39Check[0].title}" (${id39Check[0].event_date}) - Will be preserved`);
      console.log(`   Is holiday: ${id39Check[0].is_holiday ? 'Yes' : 'No'}`);
    } else {
      console.log('⚠️ ID 39 does not exist in the database');
    }
    
    // Count current holidays
    const [currentCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Current holiday count: ${currentCount[0].count}`);
    
    // Delete all holidays except ID 39
    console.log('🗑️ Removing all holidays except ID 39...');
    const [deleteResult] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${deleteResult.affectedRows} holiday records`);
    
    // Verify deletion
    const [afterCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Holidays remaining: ${afterCount[0].count}`);
    
    // Show remaining holidays (should be just ID 39 if it was a holiday)
    const [remaining] = await connection.execute('SELECT calendar_id, title, is_holiday FROM school_calendar WHERE is_holiday = 1');
    if (remaining.length > 0) {
      console.log('📋 Remaining holiday records:');
      remaining.forEach(record => {
        console.log(`  - ID: ${record.calendar_id}, Title: "${record.title}"`);
      });
    } else {
      console.log('📋 No holiday records remaining');
    }
    
    await connection.end();
    console.log('✅ Database connection closed');
    console.log('🎉 All holidays removed successfully (except ID 39)!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeAllHolidays();
