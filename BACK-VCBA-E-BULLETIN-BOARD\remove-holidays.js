const mysql = require('mysql2/promise');

async function removeHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Check ID 39 first
    const [id39] = await connection.execute('SELECT * FROM school_calendar WHERE calendar_id = 39');
    if (id39.length > 0) {
      console.log(`✅ ID 39 exists: "${id39[0].title}" (${id39[0].event_date}) - Will be preserved`);
    } else {
      console.log('⚠️ ID 39 does not exist');
    }
    
    // Count current holidays
    const [current] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Current holidays: ${current[0].count}`);
    
    // Delete all holidays except ID 39
    console.log('🗑️ Deleting all holidays except ID 39...');
    const [result] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${result.affectedRows} holiday records`);
    
    // Verify
    const [after] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Holidays remaining: ${after[0].count}`);
    
    await connection.end();
    console.log('✅ Done removing holidays!');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeHolidays();
