{"name": "jalaali-js", "version": "1.2.8", "description": "Converts <PERSON><PERSON> and <PERSON><PERSON><PERSON> calendars to each other", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/jalaali/jalaali-js"}, "keywords": ["jala<PERSON>", "jalali", "persian", "<PERSON><PERSON><PERSON><PERSON>", "shamsi", "calendar", "date"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jalaali/jalaali-js/issues"}, "homepage": "https://github.com/jalaali/jalaali-js", "devDependencies": {"browserify": "^17.0.1", "mocha": "^11.1.0", "should": "^13.2.3", "terser": "^5.39.0"}, "scripts": {"prepare": "node build-umd.js", "bench": "node bench.js", "test": "mocha test.js"}}