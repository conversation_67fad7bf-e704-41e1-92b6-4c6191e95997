const holidayService = require('./src/services/holidayService');
const mysql = require('mysql2/promise');

async function syncFreshHolidays() {
  console.log('🔄 Syncing fresh holidays from API...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // Define the years to sync
    const currentYear = new Date().getFullYear();
    const yearsToSync = [currentYear, currentYear + 1]; // Current year and next year
    
    console.log(`📅 Syncing holidays for years: ${yearsToSync.join(', ')}`);
    
    // Sync each year
    let totalCreated = 0;
    
    for (const year of yearsToSync) {
      console.log(`\n=== Syncing holidays for ${year} ===`);
      
      // Sync to database
      const syncResults = await holidayService.syncHolidaysToDatabase(year, 1); // Admin ID 1
      
      console.log(`${year} Results:`);
      console.log(`- Created: ${syncResults.created} holidays`);
      console.log(`- Updated: ${syncResults.updated} holidays`);
      console.log(`- Skipped: ${syncResults.skipped} holidays`);
      console.log(`- Errors: ${syncResults.errors.length} errors`);
      
      totalCreated += syncResults.created;
      
      if (syncResults.errors.length > 0) {
        console.log('Errors:');
        syncResults.errors.forEach(error => {
          console.log(`  - ${error.holiday}: ${error.error}`);
        });
      }
    }
    
    // Verify final results
    const [finalCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1'
    );
    
    console.log(`\n=== Final Results ===`);
    console.log(`Total holidays created: ${totalCreated}`);
    console.log(`Total holidays in database: ${finalCount[0].count}`);
    
    // Show sample of new holidays
    console.log('\n=== Sample new holidays ===');
    const [sampleHolidays] = await connection.execute(`
      SELECT calendar_id, title, event_date, category_id, is_auto_generated
      FROM school_calendar
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 10
    `);
    
    sampleHolidays.forEach(holiday => {
      const autoGen = holiday.is_auto_generated ? '(auto)' : '(manual)';
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) ${autoGen}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Fresh holiday sync completed!');
    console.log('\n💡 The calendar should now display clean, fresh holiday data!');

  } catch (error) {
    console.error('❌ Error in fresh holiday sync:', error.message);
    console.error(error.stack);
  }
}

syncFreshHolidays();
