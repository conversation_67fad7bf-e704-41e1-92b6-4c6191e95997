const mysql = require('mysql2/promise');

async function checkExisting() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('Connected to database');
    
    // Check for New Year's Day
    const [rows] = await connection.execute('SELECT * FROM school_calendar WHERE event_date = ? AND title = ? AND deleted_at IS NULL', ['2024-01-01', "New Year's Day"]);
    console.log('Existing New Year\'s Day records:');
    console.table(rows);
    
    // Check all records for 2024
    const [all2024] = await connection.execute('SELECT calendar_id, title, event_date, is_holiday, is_auto_generated FROM school_calendar WHERE YEAR(event_date) = 2024 AND deleted_at IS NULL ORDER BY event_date');
    console.log('\nAll 2024 records:');
    console.table(all2024);
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkExisting();
