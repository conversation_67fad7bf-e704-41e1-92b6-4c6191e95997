{"name": "date-holidays-parser", "version": "3.4.7", "description": "parser for worldwide holidays", "keywords": ["holidays", "parser", "world"], "homepage": "https://github.com/commenthol/date-holidays-parser", "bugs": {"url": "https://github.com/commenthol/date-holidays-parser/issues"}, "repository": {"type": "git", "url": "git+https://github.com/commenthol/date-holidays-parser.git"}, "license": "ISC", "author": "commenthol <<EMAIL>>", "maintainers": ["commenthol <<EMAIL>>"], "contributors": [], "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./lib/index.cjs"}, "./package.json": "./package.json"}, "main": "./lib/index.cjs", "module": "./src/index.js", "typings": "./types", "directories": {"lib": "lib", "doc": "docs", "test": "test"}, "mocha": {"checkLeaks": true, "colors": true}, "dependencies": {"astronomia": "^4.1.1", "caldate": "^2.0.5", "date-bengali-revised": "^2.0.2", "date-chinese": "^2.1.4", "date-easter": "^1.0.3", "deepmerge": "^4.3.1", "jalaali-js": "^1.2.7", "moment-timezone": "^0.5.47"}, "devDependencies": {"c8": "^10.1.3", "dtslint": "^4.2.1", "eslint": "^8.57.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.6.0", "hashtree": "^0.7.0", "mocha": "^11.1.0", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "rollup": "^2.79.2", "serialize-to-module": "^1.1.0", "typescript": "^5.7.3"}, "engines": {"node": ">=12.0.0"}, "c4uIgnore": {"rollup": "^2.79.1 // v3 changed exports; needs refactoring first."}, "scripts": {"all": "npm-run-all clean lint build postbuild test", "build": "rollup -c && cp scripts/rollup-fix/* lib", "postbuild": "node scripts/postbuild.cjs", "ci": "npm-run-all build test", "clean": "rimraf lib es coverage .nyc_output", "clean:all": "npm-run-all clean clean:node_modules", "clean:node_modules": "rimraf node_modules", "coverage": "c8 -r text -r html npm test", "doc": "jsdox -o docs src/Holidays.js", "lint": "eslint --fix --ext .js,.cjs .", "test": "npm-run-all test:*", "test:ci": "mocha", "__disabled__test:ts": "dtslint types", "test:tz": "TZ=UTC-10 mocha && TZ=UTC+10 mocha", "zuul": "zuul test/Holidays.test.js"}}