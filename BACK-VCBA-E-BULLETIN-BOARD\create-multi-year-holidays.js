const mysql = require('mysql2/promise');

async function createMultiYearHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Remove existing holidays except ID 39
    console.log('🗑️ Removing existing holidays...');
    const [deleteResult] = await connection.execute('DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39');
    console.log(`✅ Deleted ${deleteResult.affectedRows} holiday records`);
    
    // Holiday data
    const holidays = [
      // Philippine Holidays
      { name: "New Year's Day", date: '01-01', category_id: 8, description: 'First day of the year' },
      { name: 'First Philippine Republic Day', date: '01-23', category_id: 8, description: 'Commemorates the establishment of the First Philippine Republic' },
      { name: 'Constitution Day', date: '02-02', category_id: 8, description: 'Commemorates the ratification of the 1987 Constitution' },
      { name: 'EDSA Revolution Anniversary', date: '02-25', category_id: 8, description: 'Commemorates the People Power Revolution' },
      { name: 'Day of Valor', date: '04-09', category_id: 8, description: 'Commemorates the fall of Bataan and Corregidor' },
      { name: 'Lapu-Lapu Day', date: '04-27', category_id: 8, description: 'Commemorates the victory of Lapu-Lapu over Magellan' },
      { name: "International Workers' Day / Labour Day", date: '05-01', category_id: 8, description: "International Workers' Day" },
      { name: 'Independence Day', date: '06-12', category_id: 8, description: 'Philippine Independence Day' },
      { name: "José Rizal's birthday", date: '06-19', category_id: 8, description: 'Birthday of the national hero Dr. José Rizal' },
      { name: 'Iglesia ni Cristo Day', date: '07-27', category_id: 8, description: 'Founding anniversary of Iglesia ni Cristo' },
      { name: 'Ninoy Aquino Day', date: '08-21', category_id: 8, description: 'Commemorates the assassination of Benigno Aquino Jr.' },
      { name: "National Heroes' Day", date: '08-26', category_id: 8, description: 'Honors Filipino heroes' },
      { name: "All Saints' Day", date: '11-01', category_id: 8, description: 'Christian holiday honoring all saints' },
      { name: "All Souls' Day", date: '11-02', category_id: 8, description: 'Day of prayer for the souls of the dead' },
      { name: 'Bonifacio Day', date: '11-30', category_id: 8, description: 'Birthday of Andrés Bonifacio' },
      { name: 'Feast of the Immaculate Conception of the Blessed Virgin Mary', date: '12-08', category_id: 8, description: 'Catholic feast day' },
      { name: 'Christmas Eve', date: '12-24', category_id: 8, description: 'Day before Christmas' },
      { name: 'Christmas Day', date: '12-25', category_id: 8, description: 'Celebration of the birth of Jesus Christ' },
      { name: 'Rizal Day', date: '12-30', category_id: 8, description: 'Commemorates the execution of Dr. José Rizal' },
      { name: "New Year's Eve", date: '12-31', category_id: 8, description: 'Last day of the year' },
      
      // International Holidays
      { name: "Valentine's Day", date: '02-14', category_id: 9, description: 'Day of love and romance' },
      { name: "International Women's Day", date: '03-08', category_id: 9, description: "Celebrates women's achievements" },
      { name: 'World Environment Day', date: '06-05', category_id: 9, description: 'Promotes environmental awareness' },
      { name: 'Halloween', date: '10-31', category_id: 9, description: 'Traditional celebration of the supernatural' }
    ];
    
    // Create holidays for multiple years (2020-2030)
    const years = [2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030];
    let totalCreated = 0;
    
    console.log(`📅 Creating holidays for years: ${years.join(', ')}`);
    
    for (const year of years) {
      console.log(`\n=== Creating holidays for ${year} ===`);
      
      for (const holiday of holidays) {
        const eventDate = `${year}-${holiday.date}`;
        
        const [result] = await connection.execute(`
          INSERT INTO school_calendar (
            title, 
            description, 
            event_date, 
            category_id,
            is_recurring,
            recurrence_pattern,
            is_active,
            is_published,
            allow_comments,
            is_alert,
            is_holiday,
            holiday_type,
            country_code,
            is_auto_generated,
            api_source,
            created_by,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, 0, NULL, 1, 1, 1, 0, 1, ?, ?, 1, 'manual', 1, NOW(), NOW())
        `, [
          holiday.name,
          holiday.description,
          eventDate,
          holiday.category_id,
          holiday.category_id === 8 ? 'local' : 'international',
          holiday.category_id === 8 ? 'PH' : null
        ]);
        
        totalCreated++;
      }
      
      console.log(`✅ Created ${holidays.length} holidays for ${year}`);
    }
    
    // Verify creation
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`\n📊 Total holidays in database: ${finalCount[0].count}`);
    console.log(`🎉 Successfully created ${totalCreated} holiday records across ${years.length} years!`);
    
    // Show sample holidays by year
    console.log('\n=== Sample holidays by year ===');
    const [samples] = await connection.execute(`
      SELECT YEAR(event_date) as year, COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY YEAR(event_date)
      ORDER BY year
    `);
    
    samples.forEach(sample => {
      console.log(`- ${sample.year}: ${sample.count} holidays`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('💡 Holidays should now appear in all years!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createMultiYearHolidays();
