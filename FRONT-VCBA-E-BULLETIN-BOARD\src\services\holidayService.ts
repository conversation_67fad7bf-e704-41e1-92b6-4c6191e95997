import { httpClient } from './api.service';
import { ApiResponse } from '../types';

export interface Holiday {
  calendar_id: number;
  title: string;
  description: string;
  event_date: string;
  category_id: number;
  category_name: string;
  category_color: string;
  holiday_type: 'local' | 'international' | 'school';
  country_code: string;
  is_auto_generated: boolean;
  api_source: string;
  local_name: string;
  holiday_types: string;
  is_global: boolean;
  is_fixed: boolean;
  created_at: string;
  updated_at: string;
}

export interface HolidayStats {
  total: number;
  byCategory: Record<string, number>;
  byCountry: Record<string, number>;
  byType: Record<string, number>;
  autoGenerated: number;
  manual: number;
}

export interface SyncResults {
  created: number;
  updated: number;
  skipped: number;
  errors: Array<{ holiday: string; error: string }>;
}

export interface HolidayPreview {
  new: Holiday[];
  existing: Holiday[];
  toUpdate: Holiday[];
}

class HolidayService {
  private baseUrl = '/api/holidays';

  /**
   * Get holidays for a specific year
   */
  async getHolidays(year?: number, category?: string, countryCode?: string): Promise<Holiday[]> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());
    if (category) params.append('category', category);
    if (countryCode) params.append('country_code', countryCode);

    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}?${params.toString()}`);
    return response.data?.holidays || [];
  }

  /**
   * Get Philippine holidays for a specific year
   */
  async getPhilippineHolidays(year?: number): Promise<Holiday[]> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/philippine?${params.toString()}`);
    return response.data?.holidays || [];
  }

  /**
   * Get international holidays for a specific year
   */
  async getInternationalHolidays(year?: number): Promise<Holiday[]> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/international?${params.toString()}`);
    return response.data?.holidays || [];
  }

  /**
   * Get religious holidays for a specific year
   */
  async getReligiousHolidays(year?: number): Promise<Holiday[]> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/religious?${params.toString()}`);
    return response.data?.holidays || [];
  }

  /**
   * Get all holidays from API sources (not database)
   */
  async getAllHolidaysFromAPI(year?: number): Promise<Holiday[]> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ holidays: Holiday[] }>(`${this.baseUrl}/api-source?${params.toString()}`);
    return response.data?.holidays || [];
  }

  /**
   * Get holiday statistics
   */
  async getHolidayStats(year?: number): Promise<HolidayStats> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ stats: HolidayStats }>(`${this.baseUrl}/stats?${params.toString()}`);
    if (!response.data?.stats) {
      throw new Error('Failed to get holiday statistics');
    }
    return response.data.stats;
  }

  /**
   * Preview holidays before syncing (admin only)
   */
  async previewHolidays(year?: number): Promise<HolidayPreview> {
    const params = new URLSearchParams();
    if (year) params.append('year', year.toString());

    const response = await httpClient.get<{ preview: HolidayPreview }>(`${this.baseUrl}/preview?${params.toString()}`);
    if (!response.data?.preview) {
      throw new Error('Failed to get holiday preview');
    }
    return response.data.preview;
  }

  /**
   * Sync holidays to database (admin only)
   */
  async syncHolidays(year?: number, force = false): Promise<SyncResults> {
    const response = await httpClient.post<{ results: SyncResults }>(`${this.baseUrl}/sync`, {
      year: year || new Date().getFullYear(),
      force
    });
    if (!response.data?.results) {
      throw new Error('Failed to sync holidays');
    }
    return response.data.results;
  }

  /**
   * Sync holidays for multiple years (admin only)
   */
  async syncMultipleYears(years: number[], force = false): Promise<Record<number, SyncResults>> {
    const response = await httpClient.post<{ results: Record<number, SyncResults> }>(`${this.baseUrl}/sync-multiple`, {
      years,
      force
    });
    if (!response.data?.results) {
      throw new Error('Failed to sync holidays for multiple years');
    }
    return response.data.results;
  }

  /**
   * Delete auto-generated holidays for a specific year (admin only)
   */
  async deleteAutoGeneratedHolidays(year: number): Promise<number> {
    const response = await httpClient.delete<{ deletedCount: number }>(`${this.baseUrl}/auto-generated/${year}`);
    if (response.data?.deletedCount === undefined) {
      throw new Error('Failed to delete auto-generated holidays');
    }
    return response.data.deletedCount;
  }

  /**
   * Check if a date is a holiday
   */
  async isHoliday(date: string): Promise<boolean> {
    try {
      const year = new Date(date).getFullYear();
      const holidays = await this.getHolidays(year);
      return holidays.some(holiday => holiday.event_date === date);
    } catch (error) {
      console.error('Error checking if date is holiday:', error);
      return false;
    }
  }

  /**
   * Get holidays for a specific date
   */
  async getHolidaysForDate(date: string): Promise<Holiday[]> {
    try {
      const year = new Date(date).getFullYear();
      const holidays = await this.getHolidays(year);
      return holidays.filter(holiday => holiday.event_date === date);
    } catch (error) {
      console.error('Error getting holidays for date:', error);
      return [];
    }
  }

  /**
   * Get upcoming holidays (next 30 days)
   */
  async getUpcomingHolidays(days = 30): Promise<Holiday[]> {
    try {
      const today = new Date();
      const endDate = new Date();
      endDate.setDate(today.getDate() + days);

      const currentYear = today.getFullYear();
      const endYear = endDate.getFullYear();

      let holidays: Holiday[] = [];

      // Get holidays for current year
      holidays = await this.getHolidays(currentYear);

      // If the end date is in the next year, also get holidays for next year
      if (endYear > currentYear) {
        const nextYearHolidays = await this.getHolidays(endYear);
        holidays = [...holidays, ...nextYearHolidays];
      }

      // Filter holidays within the date range
      const todayStr = today.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      return holidays.filter(holiday => 
        holiday.event_date >= todayStr && holiday.event_date <= endDateStr
      ).sort((a, b) => a.event_date.localeCompare(b.event_date));
    } catch (error) {
      console.error('Error getting upcoming holidays:', error);
      return [];
    }
  }
}

export const holidayService = new HolidayService();
