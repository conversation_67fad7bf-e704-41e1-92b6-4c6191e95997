const mysql = require('mysql2/promise');
const https = require('https');

// Holiday data with proper categories
const holidayData = {
  philippine: [
    { name: "New Year's Day", date: "01-01", category_id: 8, description: "First day of the year" },
    { name: "First Philippine Republic Day", date: "01-23", category_id: 8, description: "Commemorates the establishment of the First Philippine Republic" },
    { name: "Constitution Day", date: "02-02", category_id: 8, description: "Commemorates the ratification of the 1987 Constitution" },
    { name: "EDSA Revolution Anniversary", date: "02-25", category_id: 8, description: "Commemorates the People Power Revolution" },
    { name: "Day of Valor", date: "04-09", category_id: 8, description: "Commemorates the fall of Bataan and Corregidor" },
    { name: "Lapu-Lapu Day", date: "04-27", category_id: 8, description: "Commemorates the victory of Lapu-Lapu over <PERSON>gellan" },
    { name: "International Workers' Day / Labour Day", date: "05-01", category_id: 8, description: "International Workers' Day" },
    { name: "Independence Day", date: "06-12", category_id: 8, description: "Philippine Independence Day" },
    { name: "<PERSON>'s birthday", date: "06-19", category_id: 8, description: "Birthday of the national hero Dr. <PERSON> <PERSON>izal" },
    { name: "Iglesia ni Cristo Day", date: "07-27", category_id: 8, description: "Founding anniversary of Iglesia ni Cristo" },
    { name: "Ninoy <PERSON> Day", date: "08-21", category_id: 8, description: "Commemorates the assassination of <PERSON>igno <PERSON> Jr." },
    { name: "National Heroes' Day", date: "08-26", category_id: 8, description: "Honors Filipino heroes" },
    { name: "All Saints' Day", date: "11-01", category_id: 8, description: "Christian holiday honoring all saints" },
    { name: "All Souls' Day", date: "11-02", category_id: 8, description: "Day of prayer for the souls of the dead" },
    { name: "Bonifacio Day", date: "11-30", category_id: 8, description: "Birthday of Andrés Bonifacio" },
    { name: "Feast of the Immaculate Conception of the Blessed Virgin Mary", date: "12-08", category_id: 8, description: "Catholic feast day" },
    { name: "Christmas Eve", date: "12-24", category_id: 8, description: "Day before Christmas" },
    { name: "Christmas Day", date: "12-25", category_id: 8, description: "Celebration of the birth of Jesus Christ" },
    { name: "Rizal Day", date: "12-30", category_id: 8, description: "Commemorates the execution of Dr. José Rizal" },
    { name: "New Year's Eve", date: "12-31", category_id: 8, description: "Last day of the year" }
  ],
  international: [
    { name: "Valentine's Day", date: "02-14", category_id: 9, description: "Day of love and romance" },
    { name: "International Women's Day", date: "03-08", category_id: 9, description: "Celebrates women's achievements" },
    { name: "World Environment Day", date: "06-05", category_id: 9, description: "Promotes environmental awareness" },
    { name: "Halloween", date: "10-31", category_id: 9, description: "Traditional celebration of the supernatural" }
  ]
};

async function createRecurringHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    console.log('🔄 Creating recurring holidays...\n');
    
    let totalCreated = 0;
    const currentYear = new Date().getFullYear();
    
    // Create Philippine holidays
    console.log('=== Creating Philippine Holidays ===');
    for (const holiday of holidayData.philippine) {
      const eventDate = `${currentYear}-${holiday.date}`;
      
      const [result] = await connection.execute(`
        INSERT INTO school_calendar (
          title, 
          description, 
          event_date, 
          category_id,
          is_recurring,
          recurrence_pattern,
          is_active,
          is_published,
          allow_comments,
          is_alert,
          is_holiday,
          holiday_type,
          country_code,
          is_auto_generated,
          api_source,
          created_by,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'local', 'PH', 1, 'manual', 1, NOW(), NOW())
      `, [
        holiday.name,
        holiday.description,
        eventDate,
        holiday.category_id
      ]);
      
      console.log(`✅ Created: ${holiday.name} (ID: ${result.insertId})`);
      totalCreated++;
    }
    
    // Create International holidays
    console.log('\n=== Creating International Holidays ===');
    for (const holiday of holidayData.international) {
      const eventDate = `${currentYear}-${holiday.date}`;
      
      const [result] = await connection.execute(`
        INSERT INTO school_calendar (
          title, 
          description, 
          event_date, 
          category_id,
          is_recurring,
          recurrence_pattern,
          is_active,
          is_published,
          allow_comments,
          is_alert,
          is_holiday,
          holiday_type,
          country_code,
          is_auto_generated,
          api_source,
          created_by,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'international', NULL, 1, 'manual', 1, NOW(), NOW())
      `, [
        holiday.name,
        holiday.description,
        eventDate,
        holiday.category_id
      ]);
      
      console.log(`✅ Created: ${holiday.name} (ID: ${result.insertId})`);
      totalCreated++;
    }
    
    // Verify creation
    console.log('\n=== Verification ===');
    const [holidayCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    console.log(`📊 Total holidays in database: ${holidayCount[0].count}`);
    
    const [recurringCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_recurring = 1 AND is_holiday = 1');
    console.log(`🔄 Recurring holidays: ${recurringCount[0].count}`);
    
    // Show sample holidays
    console.log('\n=== Sample Created Holidays ===');
    const [samples] = await connection.execute(`
      SELECT sc.calendar_id, sc.title, sc.event_date, sc.is_recurring, sc.recurrence_pattern, c.name as category_name
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE sc.is_holiday = 1
      ORDER BY sc.event_date
      LIMIT 10
    `);
    
    samples.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) [${holiday.category_name}] Recurring: ${holiday.recurrence_pattern}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log(`🎉 Successfully created ${totalCreated} recurring holidays!`);
    console.log('\n💡 These holidays will now appear every year automatically due to the recurring pattern!');
    
  } catch (error) {
    console.error('❌ Error creating holidays:', error.message);
  }
}

createRecurringHolidays();
