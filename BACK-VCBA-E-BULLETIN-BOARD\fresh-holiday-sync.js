const mysql = require('mysql2/promise');
const holidayService = require('./src/services/holidayService');

async function freshHolidaySync() {
  console.log('🔄 Removing all holidays and creating fresh entries from API...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // First, check if ID 39 exists and if it's a holiday
    console.log('\n=== Checking ID 39 ===');
    const [id39Check] = await connection.execute(
      'SELECT * FROM school_calendar WHERE calendar_id = 39'
    );

    if (id39Check.length === 0) {
      console.log('⚠️ ID 39 does not exist in the database');
    } else {
      console.log(`✅ Found ID 39: "${id39Check[0].title}" (${id39Check[0].event_date})`);
      console.log(`Is holiday: ${id39Check[0].is_holiday ? 'Yes' : 'No'}`);
    }

    // Count current holidays
    const [currentHolidays] = await connection.execute(
      'SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1'
    );
    console.log(`\nCurrent holiday count: ${currentHolidays[0].count}`);

    // Delete all holidays except ID 39
    console.log('\n=== Removing existing holidays ===');
    const [deleteResult] = await connection.execute(
      'DELETE FROM school_calendar WHERE is_holiday = 1 AND calendar_id != 39'
    );

    console.log(`✅ Removed ${deleteResult.affectedRows} holiday records`);

    // Verify deletion
    const [afterDelete] = await connection.execute(
      'SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1'
    );
    console.log(`Holidays remaining after deletion: ${afterDelete[0].count}`);

    // Now sync fresh holidays from API
    console.log('\n=== Syncing fresh holidays from API ===');
    
    // Define the years to sync
    const currentYear = new Date().getFullYear();
    const yearsToSync = [currentYear, currentYear + 1]; // Current year and next year
    
    console.log(`Syncing holidays for years: ${yearsToSync.join(', ')}`);
    
    // Get category map
    const categoryMap = await holidayService.ensureHolidayCategories();
    console.log('Holiday categories ensured');
    
    // Sync each year
    let totalCreated = 0;
    
    for (const year of yearsToSync) {
      console.log(`\n=== Syncing holidays for ${year} ===`);
      
      // Get holidays from API
      const philippineHolidays = holidayService.getPhilippineHolidays(year);
      const internationalHolidays = holidayService.getInternationalHolidays(year);
      
      // Combine all holidays
      const allHolidays = [...philippineHolidays, ...internationalHolidays];
      console.log(`Found ${allHolidays.length} holidays for ${year} from API`);
      
      // Sync to database
      const syncResults = await holidayService.syncHolidaysToDatabase(year, 1); // Admin ID 1
      
      console.log(`${year} Results:`);
      console.log(`- Created: ${syncResults.created} holidays`);
      console.log(`- Updated: ${syncResults.updated} holidays`);
      console.log(`- Skipped: ${syncResults.skipped} holidays`);
      console.log(`- Errors: ${syncResults.errors.length} errors`);
      
      totalCreated += syncResults.created;
      
      if (syncResults.errors.length > 0) {
        console.log('Errors:');
        syncResults.errors.forEach(error => {
          console.log(`  - ${error.holiday}: ${error.error}`);
        });
      }
    }
    
    // Verify final results
    const [finalCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1'
    );
    
    console.log(`\n=== Final Results ===`);
    console.log(`Total holidays created: ${totalCreated}`);
    console.log(`Total holidays in database: ${finalCount[0].count}`);
    
    // Show sample of new holidays
    console.log('\n=== Sample new holidays ===');
    const [sampleHolidays] = await connection.execute(`
      SELECT calendar_id, title, event_date, category_name, is_auto_generated
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 10
    `);
    
    sampleHolidays.forEach(holiday => {
      const autoGen = holiday.is_auto_generated ? '(auto)' : '(manual)';
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) [${holiday.category_name}] ${autoGen}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Fresh holiday sync completed!');
    console.log('\n💡 The calendar should now display clean, fresh holiday data!');

  } catch (error) {
    console.error('❌ Error in fresh holiday sync:', error.message);
    console.error(error.stack);
  }
}

freshHolidaySync();
