const mysql = require('mysql2/promise');

async function addMissingHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    const currentYear = new Date().getFullYear();
    const philippineCategoryId = 8; // Philippine Holidays category
    
    // Missing Philippine holidays
    const missingHolidays = [
      { 
        name: 'Iglesia ni Cristo Day', 
        date: `${currentYear}-07-27`, 
        description: 'Special working holiday commemorating the founding of Iglesia ni Cristo church' 
      },
      { 
        name: 'EDSA People Power Anniversary', 
        date: `${currentYear}-02-25`, 
        description: 'Commemorates the 1986 People Power Revolution that ended the Marcos dictatorship' 
      }
    ];
    
    console.log('🔍 Checking for missing Philippine holidays...');
    
    let added = 0;
    
    for (const holiday of missingHolidays) {
      // Check if holiday already exists
      const [existing] = await connection.execute(
        'SELECT calendar_id FROM school_calendar WHERE title = ? AND YEAR(event_date) = ?',
        [holiday.name, currentYear]
      );
      
      if (existing.length === 0) {
        const [result] = await connection.execute(`
          INSERT INTO school_calendar (
            title, description, event_date, category_id,
            is_recurring, recurrence_pattern, is_active, is_published,
            allow_comments, is_alert, is_holiday, holiday_type,
            country_code, is_auto_generated, api_source, created_by,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'local', 'PH', 1, 'manual', 1, NOW(), NOW())
        `, [holiday.name, holiday.description, holiday.date, philippineCategoryId]);
        
        console.log(`✅ Added: ${holiday.name} (${holiday.date}) - ID: ${result.insertId}`);
        added++;
      } else {
        console.log(`⚠️ Already exists: ${holiday.name}`);
      }
    }
    
    // Final count
    const [totalCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [philippineCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1 AND category_id = 8');
    
    console.log(`\n📊 Summary:`);
    console.log(`- Missing holidays added: ${added}`);
    console.log(`- Total Philippine holidays: ${philippineCount[0].count}`);
    console.log(`- Total holidays in database: ${totalCount[0].count}`);
    
    // Show all Philippine holidays
    console.log('\n🇵🇭 Complete Philippine holidays list:');
    const [allPhilippine] = await connection.execute(`
      SELECT title, event_date 
      FROM school_calendar 
      WHERE is_holiday = 1 AND category_id = 8
      ORDER BY event_date
    `);
    
    allPhilippine.forEach((holiday, index) => {
      const date = new Date(holiday.event_date);
      const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      console.log(`${index + 1}. ${holiday.title} (${monthDay})`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Missing holidays check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

addMissingHolidays();
