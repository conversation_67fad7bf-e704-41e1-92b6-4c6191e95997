const http = require('http');

function testAPI(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testHolidayAPI() {
  console.log('Testing Holiday API endpoints...\n');

  const endpoints = [
    '/api/holidays/stats?year=2024',
    '/api/holidays?year=2024',
    '/api/holidays/philippine?year=2024',
    '/api/holidays/international?year=2024'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing: ${endpoint}`);
      const result = await testAPI(endpoint);
      console.log(`Status: ${result.status}`);
      
      if (result.status === 200 && typeof result.data === 'object') {
        if (endpoint.includes('stats')) {
          console.log(`Stats: Total holidays: ${result.data.data?.stats?.total || 'N/A'}`);
        } else {
          console.log(`Holidays found: ${result.data.data?.holidays?.length || 'N/A'}`);
        }
      } else {
        console.log(`Response: ${JSON.stringify(result.data).substring(0, 200)}...`);
      }
      console.log('---');
    } catch (error) {
      console.log(`Error: ${error.message}`);
      console.log('---');
    }
  }
}

testHolidayAPI();
