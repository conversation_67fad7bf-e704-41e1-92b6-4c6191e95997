const CalendarModel = require('./src/models/CalendarModel');

async function testYearEvents() {
  console.log('Testing calendar events for entire year...\n');

  try {
    // Test getting events for entire year 2024
    console.log('=== Testing 2024 (entire year) ===');
    const events2024 = await CalendarModel.getCalendarEvents(2024);
    
    console.log(`Total event dates: ${Object.keys(events2024).length}`);
    
    // Count total events (some dates may have multiple events)
    let totalEvents = 0;
    let holidayEvents = 0;
    let regularEvents = 0;
    
    Object.entries(events2024).forEach(([date, dayEvents]) => {
      totalEvents += dayEvents.length;
      dayEvents.forEach(event => {
        if (event.is_holiday) {
          holidayEvents++;
        } else {
          regularEvents++;
        }
      });
    });
    
    console.log(`Total events: ${totalEvents}`);
    console.log(`Holiday events: ${holidayEvents}`);
    console.log(`Regular events: ${regularEvents}`);
    
    // Show sample events from different months
    console.log('\n=== Sample events by month ===');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    for (let month = 1; month <= 12; month++) {
      const monthStr = month.toString().padStart(2, '0');
      const monthEvents = Object.entries(events2024)
        .filter(([date]) => date.startsWith(`2024-${monthStr}`))
        .slice(0, 3); // First 3 events of the month
      
      if (monthEvents.length > 0) {
        console.log(`\n${monthNames[month-1]} 2024:`);
        monthEvents.forEach(([date, dayEvents]) => {
          dayEvents.forEach(event => {
            const type = event.is_holiday ? '🎉' : '📅';
            console.log(`  ${type} ${date}: ${event.title} [${event.category_name}]`);
          });
        });
      }
    }
    
    // Test performance
    console.log('\n=== Performance Test ===');
    const startTime = Date.now();
    await CalendarModel.getCalendarEvents(2024);
    const endTime = Date.now();
    console.log(`Query time: ${endTime - startTime}ms`);
    
    if (endTime - startTime > 1000) {
      console.log('⚠️  Query took more than 1 second. Consider adding database indexes.');
    } else {
      console.log('✅ Query performance is good.');
    }

  } catch (error) {
    console.error('Error testing year events:', error);
  }
}

testYearEvents();
