const mysql = require('mysql2/promise');

async function checkHolidayYears() {
  console.log('Checking what years have holidays in the database...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // Check holidays by year
    console.log('\n=== Holidays by Year ===');
    const [yearRows] = await connection.execute(`
      SELECT 
        YEAR(event_date) as year,
        COUNT(*) as total_events,
        SUM(CASE WHEN is_holiday = 1 THEN 1 ELSE 0 END) as holiday_events,
        SUM(CASE WHEN is_holiday = 0 THEN 1 ELSE 0 END) as regular_events
      FROM school_calendar 
      GROUP BY YEAR(event_date) 
      ORDER BY year
    `);

    console.log('Year breakdown:');
    yearRows.forEach(row => {
      console.log(`- ${row.year}: ${row.total_events} total (${row.holiday_events} holidays, ${row.regular_events} regular)`);
    });

    // Check what years we need for the calendar (current year ± 2)
    const currentYear = new Date().getFullYear();
    const neededYears = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1, currentYear + 2];
    
    console.log(`\n=== Years needed for calendar (${currentYear} ± 2) ===`);
    console.log(`Needed years: ${neededYears.join(', ')}`);
    
    const existingYears = yearRows.map(row => row.year);
    const missingYears = neededYears.filter(year => !existingYears.includes(year));
    const yearsWithoutHolidays = [];
    
    console.log('\nYear status:');
    neededYears.forEach(year => {
      const yearData = yearRows.find(row => row.year === year);
      if (!yearData) {
        console.log(`- ${year}: ❌ No events at all`);
        missingYears.push(year);
      } else if (yearData.holiday_events === 0) {
        console.log(`- ${year}: ⚠️  Has ${yearData.total_events} events but no holidays`);
        yearsWithoutHolidays.push(year);
      } else {
        console.log(`- ${year}: ✅ Has ${yearData.holiday_events} holidays and ${yearData.regular_events} regular events`);
      }
    });

    if (missingYears.length > 0) {
      console.log(`\n🔍 Missing years that need holiday sync: ${missingYears.join(', ')}`);
    }
    
    if (yearsWithoutHolidays.length > 0) {
      console.log(`\n🔍 Years with events but no holidays: ${yearsWithoutHolidays.join(', ')}`);
    }

    // Show sample holidays from existing years
    console.log('\n=== Sample holidays from existing years ===');
    const [sampleHolidays] = await connection.execute(`
      SELECT 
        YEAR(event_date) as year,
        event_date,
        title,
        category_name,
        is_auto_generated
      FROM school_calendar sc
      LEFT JOIN categories c ON sc.category_id = c.category_id
      WHERE is_holiday = 1 
      ORDER BY event_date 
      LIMIT 10
    `);

    sampleHolidays.forEach(holiday => {
      const autoGen = holiday.is_auto_generated ? '(auto)' : '(manual)';
      console.log(`- ${holiday.event_date}: ${holiday.title} [${holiday.category_name}] ${autoGen}`);
    });

    await connection.end();
    console.log('\n✅ Database connection closed');

  } catch (error) {
    console.error('❌ Error checking holiday years:', error.message);
  }
}

checkHolidayYears();
