const https = require('https');
const mysql = require('mysql2/promise');

async function createHolidaysFromAPI() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Function to fetch holidays from API
    function fetchHolidays(year, country = 'PH') {
      return new Promise((resolve, reject) => {
        const url = `https://date.nager.at/api/v3/PublicHolidays/${year}/${country}`;
        
        https.get(url, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            try {
              const holidays = JSON.parse(data);
              resolve(holidays);
            } catch (error) {
              reject(error);
            }
          });
        }).on('error', reject);
      });
    }
    
    // Get current year
    const currentYear = new Date().getFullYear();
    console.log(`📅 Fetching holidays for ${currentYear} from API...`);
    
    // Fetch Philippine holidays
    const holidays = await fetchHolidays(currentYear, 'PH');
    console.log(`✅ Fetched ${holidays.length} holidays from API`);
    
    // Get category IDs (assuming Philippine Holidays = 8, International = 9)
    const [categories] = await connection.execute('SELECT * FROM categories WHERE name LIKE "%Holiday%" ORDER BY category_id');
    console.log('📋 Available categories:');
    categories.forEach(cat => {
      console.log(`  - ID: ${cat.category_id}, Name: "${cat.name}"`);
    });
    
    const philippineCategoryId = categories.find(cat => cat.name.includes('Philippine'))?.category_id || 8;
    
    let created = 0;
    let skipped = 0;
    
    console.log('\n🔄 Creating holidays...');
    
    for (const holiday of holidays) {
      try {
        // Check if holiday already exists (prevent duplicates)
        const [existing] = await connection.execute(
          'SELECT calendar_id FROM school_calendar WHERE title = ? AND YEAR(event_date) = ?',
          [holiday.name, currentYear]
        );
        
        if (existing.length > 0) {
          console.log(`⚠️ Skipped duplicate: ${holiday.name}`);
          skipped++;
          continue;
        }
        
        // Insert holiday with recurring settings
        const [result] = await connection.execute(`
          INSERT INTO school_calendar (
            title, 
            description, 
            event_date, 
            category_id,
            is_recurring,
            recurrence_pattern,
            is_active,
            is_published,
            allow_comments,
            is_alert,
            is_holiday,
            holiday_type,
            country_code,
            is_auto_generated,
            api_source,
            created_by,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'local', 'PH', 1, 'nager_api', 1, NOW(), NOW())
        `, [
          holiday.name,
          holiday.localName || holiday.name,
          holiday.date,
          philippineCategoryId
        ]);
        
        console.log(`✅ Created: ${holiday.name} (ID: ${result.insertId}) - Date: ${holiday.date}`);
        created++;
        
      } catch (error) {
        console.log(`❌ Error creating ${holiday.name}: ${error.message}`);
      }
    }
    
    // Add some international holidays manually
    const internationalHolidays = [
      { name: "Valentine's Day", date: `${currentYear}-02-14`, description: 'Day of love and romance' },
      { name: 'Halloween', date: `${currentYear}-10-31`, description: 'Traditional celebration' },
      { name: "International Women's Day", date: `${currentYear}-03-08`, description: "Celebrates women's achievements" }
    ];
    
    const internationalCategoryId = categories.find(cat => cat.name.includes('International'))?.category_id || 9;
    
    console.log('\n🌍 Adding international holidays...');
    
    for (const holiday of internationalHolidays) {
      try {
        // Check for duplicates
        const [existing] = await connection.execute(
          'SELECT calendar_id FROM school_calendar WHERE title = ? AND YEAR(event_date) = ?',
          [holiday.name, currentYear]
        );
        
        if (existing.length > 0) {
          console.log(`⚠️ Skipped duplicate: ${holiday.name}`);
          skipped++;
          continue;
        }
        
        const [result] = await connection.execute(`
          INSERT INTO school_calendar (
            title, 
            description, 
            event_date, 
            category_id,
            is_recurring,
            recurrence_pattern,
            is_active,
            is_published,
            allow_comments,
            is_alert,
            is_holiday,
            holiday_type,
            country_code,
            is_auto_generated,
            api_source,
            created_by,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, 1, 'yearly', 1, 1, 1, 0, 1, 'international', NULL, 1, 'manual', 1, NOW(), NOW())
        `, [
          holiday.name,
          holiday.description,
          holiday.date,
          internationalCategoryId
        ]);
        
        console.log(`✅ Created: ${holiday.name} (ID: ${result.insertId}) - Date: ${holiday.date}`);
        created++;
        
      } catch (error) {
        console.log(`❌ Error creating ${holiday.name}: ${error.message}`);
      }
    }
    
    // Final verification
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1');
    const [recurringCount] = await connection.execute('SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1 AND is_recurring = 1');
    
    console.log('\n📊 Final Results:');
    console.log(`- Total holidays created: ${created}`);
    console.log(`- Duplicates skipped: ${skipped}`);
    console.log(`- Total holidays in database: ${finalCount[0].count}`);
    console.log(`- Recurring holidays: ${recurringCount[0].count}`);
    
    // Show sample holidays
    console.log('\n📋 Sample created holidays:');
    const [samples] = await connection.execute(`
      SELECT calendar_id, title, event_date, is_recurring, recurrence_pattern
      FROM school_calendar 
      WHERE is_holiday = 1
      ORDER BY event_date
      LIMIT 10
    `);
    
    samples.forEach(holiday => {
      console.log(`- ID: ${holiday.calendar_id}, "${holiday.title}" (${holiday.event_date}) - Recurring: ${holiday.recurrence_pattern}`);
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Holidays created successfully from API with recurring pattern!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createHolidaysFromAPI();
