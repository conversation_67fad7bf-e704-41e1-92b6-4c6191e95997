const mysql = require('mysql2/promise');

async function testRecurringSystem() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Test 1: Check what holidays exist for 2025
    console.log('\n🔍 Test 1: Holidays in 2025');
    const [holidays2025] = await connection.execute(`
      SELECT calendar_id, title, event_date, is_recurring, recurrence_pattern
      FROM school_calendar 
      WHERE is_holiday = 1 AND YEAR(event_date) = 2025
      ORDER BY event_date
      LIMIT 5
    `);
    
    console.log(`Found ${holidays2025.length} holidays in 2025:`);
    holidays2025.forEach(holiday => {
      console.log(`- ${holiday.title} (${holiday.event_date}) - Recurring: ${holiday.is_recurring}, Pattern: ${holiday.recurrence_pattern}`);
    });
    
    // Test 2: Check what holidays exist for 2026 (should be NONE if recurring doesn't work)
    console.log('\n🔍 Test 2: Holidays in 2026 (should be EMPTY if recurring not working)');
    const [holidays2026] = await connection.execute(`
      SELECT calendar_id, title, event_date, is_recurring, recurrence_pattern
      FROM school_calendar 
      WHERE is_holiday = 1 AND YEAR(event_date) = 2026
      ORDER BY event_date
    `);
    
    console.log(`Found ${holidays2026.length} holidays in 2026:`);
    if (holidays2026.length === 0) {
      console.log('❌ NO HOLIDAYS IN 2026 - This confirms recurring is NOT working!');
    } else {
      holidays2026.forEach(holiday => {
        console.log(`- ${holiday.title} (${holiday.event_date})`);
      });
    }
    
    // Test 3: Check what holidays exist for 2024 (should be NONE if recurring doesn't work)
    console.log('\n🔍 Test 3: Holidays in 2024 (should be EMPTY if recurring not working)');
    const [holidays2024] = await connection.execute(`
      SELECT calendar_id, title, event_date, is_recurring, recurrence_pattern
      FROM school_calendar 
      WHERE is_holiday = 1 AND YEAR(event_date) = 2024
      ORDER BY event_date
    `);
    
    console.log(`Found ${holidays2024.length} holidays in 2024:`);
    if (holidays2024.length === 0) {
      console.log('❌ NO HOLIDAYS IN 2024 - This confirms recurring is NOT working!');
    } else {
      holidays2024.forEach(holiday => {
        console.log(`- ${holiday.title} (${holiday.event_date})`);
      });
    }
    
    // Test 4: Show the problem - holidays only exist for 2025
    console.log('\n📊 Summary of the problem:');
    const [yearCounts] = await connection.execute(`
      SELECT YEAR(event_date) as year, COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY YEAR(event_date)
      ORDER BY year
    `);
    
    console.log('Holiday distribution by year:');
    yearCounts.forEach(row => {
      console.log(`- ${row.year}: ${row.count} holidays`);
    });
    
    if (yearCounts.length === 1 && yearCounts[0].year === 2025) {
      console.log('\n❌ PROBLEM CONFIRMED:');
      console.log('- All holidays only exist for 2025');
      console.log('- No holidays in other years (2024, 2026, etc.)');
      console.log('- The is_recurring=1 and recurrence_pattern=yearly fields are being IGNORED');
      console.log('- The calendar system does NOT implement recurring logic');
      
      console.log('\n💡 SOLUTIONS:');
      console.log('1. Implement recurring logic in the backend (complex)');
      console.log('2. Create actual holiday records for multiple years (simple)');
      console.log('3. Modify the calendar query to generate recurring events on-the-fly (medium)');
    }
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testRecurringSystem();
