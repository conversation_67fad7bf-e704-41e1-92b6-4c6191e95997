const holidayService = require('./src/services/holidayService');

async function simpleSyncTest() {
  console.log('Simple Sync Test...\n');

  try {
    // Get just a few holidays to test
    const philippineHolidays = holidayService.getPhilippineHolidays(2024);
    const testHolidays = philippineHolidays.slice(0, 3); // Just first 3 holidays
    
    console.log('Test holidays:');
    testHolidays.forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date})`);
    });

    // Get category map
    const categoryMap = await holidayService.ensureHolidayCategories();
    console.log('\nCategory map:', categoryMap);

    // Sync each holiday individually
    console.log('\n=== Syncing holidays individually ===');
    for (const holiday of testHolidays) {
      try {
        console.log(`\nSyncing: ${holiday.name}`);
        
        // Check if exists
        const existing = await holidayService.findExistingHoliday(holiday.date, holiday.name);
        console.log('Existing:', existing ? `ID ${existing.calendar_id}` : 'None');
        
        if (!existing) {
          // Create new
          const result = await holidayService.createHolidayInDatabase(holiday, categoryMap, 1);
          console.log('Created:', result);
        } else {
          console.log('Skipped - already exists');
        }
      } catch (error) {
        console.error(`Error syncing ${holiday.name}:`, error.message);
      }
    }

    // Check what was created
    console.log('\n=== Checking database ===');
    const dbHolidays = await holidayService.getHolidaysFromDatabase(2024);
    console.log(`Found ${dbHolidays.length} holidays in database`);
    
    dbHolidays.forEach(holiday => {
      console.log(`- ${holiday.title} (${holiday.event_date}) - Holiday: ${holiday.is_holiday}, Auto: ${holiday.is_auto_generated}`);
    });

  } catch (error) {
    console.error('Error in simple sync test:', error);
  }
}

simpleSyncTest();
