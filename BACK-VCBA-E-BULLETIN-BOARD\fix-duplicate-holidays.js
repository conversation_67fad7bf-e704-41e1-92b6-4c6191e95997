const mysql = require('mysql2/promise');

async function fixDuplicateHolidays() {
  console.log('🔍 Checking and fixing duplicate holidays...\n');

  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });

    console.log('✅ Connected to database');

    // Find duplicate holidays (same title and date)
    console.log('\n=== Finding duplicate holidays ===');
    const [duplicates] = await connection.execute(`
      SELECT 
        title,
        event_date,
        COUNT(*) as count,
        GROUP_CONCAT(calendar_id ORDER BY calendar_id) as ids,
        GROUP_CONCAT(is_auto_generated ORDER BY calendar_id) as auto_generated_flags,
        GROUP_CONCAT(is_holiday ORDER BY calendar_id) as holiday_flags
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title, event_date
      HAVING COUNT(*) > 1
      ORDER BY count DESC, event_date
    `);

    if (duplicates.length === 0) {
      console.log('✅ No duplicate holidays found');
      await connection.end();
      return;
    }

    console.log(`⚠️ Found ${duplicates.length} sets of duplicate holidays:`);
    duplicates.forEach(dup => {
      console.log(`- "${dup.title}" on ${dup.event_date}: ${dup.count} copies (IDs: ${dup.ids})`);
      console.log(`  Auto-generated flags: ${dup.auto_generated_flags}`);
      console.log(`  Holiday flags: ${dup.holiday_flags}`);
    });

    // Ask for confirmation before proceeding
    console.log('\n⚠️ WARNING: This script will delete duplicate holidays, keeping only one copy of each.');
    console.log('The script will keep the record with the lowest ID (oldest) for each duplicate set.');
    
    // Prepare to fix duplicates
    console.log('\n=== Fixing duplicate holidays ===');
    
    let totalDeleted = 0;
    let totalKept = 0;
    
    // For each set of duplicates
    for (const dup of duplicates) {
      const ids = dup.ids.split(',').map(id => parseInt(id));
      const keepId = Math.min(...ids); // Keep the lowest ID
      const deleteIds = ids.filter(id => id !== keepId);
      
      console.log(`\nProcessing "${dup.title}" on ${dup.event_date}:`);
      console.log(`- Keeping ID: ${keepId}`);
      console.log(`- Deleting IDs: ${deleteIds.join(', ')}`);
      
      // Delete duplicates
      if (deleteIds.length > 0) {
        const [deleteResult] = await connection.execute(
          `DELETE FROM school_calendar WHERE calendar_id IN (?)`,
          [deleteIds]
        );
        
        console.log(`✅ Deleted ${deleteResult.affectedRows} duplicate records`);
        totalDeleted += deleteResult.affectedRows;
        totalKept++;
      }
    }
    
    // Verify the fix
    console.log('\n=== Verifying fix ===');
    const [afterFix] = await connection.execute(`
      SELECT 
        title,
        event_date,
        COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1
      GROUP BY title, event_date
      HAVING COUNT(*) > 1
    `);
    
    if (afterFix.length === 0) {
      console.log('✅ All duplicates successfully removed!');
    } else {
      console.log(`⚠️ There are still ${afterFix.length} sets of duplicates. May need manual intervention.`);
    }
    
    // Summary
    console.log('\n=== Summary ===');
    console.log(`Total duplicate sets found: ${duplicates.length}`);
    console.log(`Total records kept: ${totalKept}`);
    console.log(`Total duplicate records deleted: ${totalDeleted}`);
    
    // Get total holiday count after fix
    const [totalCount] = await connection.execute(`
      SELECT COUNT(*) as count FROM school_calendar WHERE is_holiday = 1
    `);
    
    console.log(`Total holidays in database after fix: ${totalCount[0].count}`);
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('🎉 Duplicate holiday fix completed!');

  } catch (error) {
    console.error('❌ Error fixing duplicate holidays:', error.message);
  }
}

fixDuplicateHolidays();
