const http = require('http');

function testServer() {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/calendar/events',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response length:', data.length);
      console.log('Response preview:', data.substring(0, 200));
    });
  });

  req.on('error', (error) => {
    console.error('Request error:', error.message);
  });

  req.setTimeout(5000, () => {
    console.log('Request timeout');
    req.destroy();
  });

  req.end();
}

console.log('Testing server connection...');
testServer();
