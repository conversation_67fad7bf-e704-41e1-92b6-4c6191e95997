import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider } from './contexts';
import { AdminAuthProvider } from './contexts/AdminAuthContext';
import { StudentAuthProvider } from './contexts/StudentAuthContext';
import { ToastProvider } from './contexts/ToastContext';
import { ProtectedRoute, PublicRoute } from './components/common';
import { ErrorBoundary } from './components/ErrorBoundary';
import './styles/commentDepth.css';
import { AdminLogin, StudentLogin, AdminRegister } from './pages';
import AdminLayout from './components/admin/layout/AdminLayout';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminNewsfeed from './pages/admin/AdminNewsfeed';
import Calendar from './pages/admin/Calendar';
import PostManagement from './pages/admin/PostManagement';
import StudentManagement from './pages/admin/StudentManagement';
import Settings from './pages/admin/Settings';
import ApiTest from './pages/debug/ApiTest';
import StudentLayout from './components/student/layout/StudentLayout';
import StudentDashboard from './pages/student/StudentDashboard';
import StudentNewsfeed from './pages/student/StudentNewsfeed';
import StudentSettings from './pages/student/StudentSettings';
import './App.css';

// Smart redirect component that determines the appropriate login page based on the current path
const SmartRedirect: React.FC = () => {
  const location = useLocation();

  // If the path starts with /student, redirect to student login
  if (location.pathname.startsWith('/student')) {
    return <Navigate to="/student/login" replace />;
  }

  // Default to admin login for all other paths
  return <Navigate to="/admin/login" replace />;
};

// Admin Routes Component with isolated auth context
const AdminRoutes: React.FC = () => (
  <AdminAuthProvider>
    <Routes>
      {/* Admin public routes */}
      <Route
        path="/login"
        element={
          <PublicRoute restricted>
            <AdminLogin />
          </PublicRoute>
        }
      />

      <Route
        path="/register"
        element={
          <PublicRoute restricted>
            <ErrorBoundary>
              <AdminRegister />
            </ErrorBoundary>
          </PublicRoute>
        }
      />

      {/* Admin protected routes with layout */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminLayout>
              <AdminDashboard />
            </AdminLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/newsfeed"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminNewsfeed />
          </ProtectedRoute>
        }
      />
      <Route
        path="/calendar"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminLayout>
              <Calendar />
            </AdminLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/posts"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminLayout>
              <PostManagement />
            </AdminLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/student-management"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminLayout>
              <StudentManagement />
            </AdminLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminLayout>
              <Settings />
            </AdminLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/debug"
        element={
          <ProtectedRoute requiredRole="admin">
            <ApiTest />
          </ProtectedRoute>
        }
      />
      <Route
        path="/"
        element={
          <ProtectedRoute requiredRole="admin">
            <Navigate to="/admin/dashboard" replace />
          </ProtectedRoute>
        }
      />
    </Routes>
  </AdminAuthProvider>
);

// Student Routes Component with isolated auth context
const StudentRoutes: React.FC = () => (
  <StudentAuthProvider>
    <Routes>
      {/* Student public routes */}
      <Route
        path="/login"
        element={
          <PublicRoute restricted>
            <StudentLogin />
          </PublicRoute>
        }
      />

      {/* Student protected routes with layout */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute requiredRole="student">
            <StudentLayout>
              <StudentDashboard />
            </StudentLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/newsfeed"
        element={
          <ProtectedRoute requiredRole="student">
            <StudentNewsfeed />
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute requiredRole="student">
            <StudentLayout>
              <StudentSettings />
            </StudentLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/"
        element={
          <ProtectedRoute requiredRole="student">
            <Navigate to="/student/newsfeed" replace />
          </ProtectedRoute>
        }
      />
    </Routes>
  </StudentAuthProvider>
);

function App() {
  return (
    <ToastProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Default redirect to admin login */}
            <Route path="/" element={<Navigate to="/admin/login" replace />} />

            {/* Admin routes with isolated auth context */}
            <Route path="/admin/*" element={<AdminRoutes />} />

            {/* Student routes with isolated auth context */}
            <Route path="/student/*" element={<StudentRoutes />} />

            {/* Catch all route - smart redirect based on path */}
            <Route path="*" element={<SmartRedirect />} />
          </Routes>
        </div>
      </Router>
    </ToastProvider>
  );
}

export default App;
