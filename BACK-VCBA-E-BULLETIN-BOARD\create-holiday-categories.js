const mysql = require('mysql2/promise');

async function createHolidayCategories() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('Connected to database');
    
    const categories = [
      {
        name: 'Philippine Holidays',
        description: 'Official Philippine holidays including regular holidays, special non-working holidays, and special working holidays',
        color_code: '#FF6B6B'
      },
      {
        name: 'International Holidays',
        description: 'Internationally recognized holidays such as New Year\'s Day, Christmas, Easter, Valentine\'s Day, etc.',
        color_code: '#4ECDC4'
      },
      {
        name: 'Religious Holidays',
        description: 'Religious observances from various faiths including Christian, Islamic, Hindu, Jewish, and Buddhist holidays',
        color_code: '#45B7D1'
      }
    ];

    for (const category of categories) {
      // Check if category already exists
      const [existing] = await connection.execute(
        'SELECT category_id FROM categories WHERE name = ?',
        [category.name]
      );

      if (existing.length > 0) {
        console.log(`Category "${category.name}" already exists with ID ${existing[0].category_id}`);
        continue;
      }

      // Insert new category
      const [result] = await connection.execute(
        'INSERT INTO categories (name, description, color_code, is_active, created_at, updated_at) VALUES (?, ?, ?, 1, NOW(), NOW())',
        [category.name, category.description, category.color_code]
      );

      console.log(`Created category "${category.name}" with ID ${result.insertId}`);
    }

    // Show all categories
    console.log('\nAll categories:');
    const [allCategories] = await connection.execute('SELECT * FROM categories ORDER BY category_id');
    console.table(allCategories);
    
    await connection.end();
    console.log('\nHoliday categories setup completed!');
    
  } catch (error) {
    console.error('Error creating holiday categories:', error);
  }
}

createHolidayCategories();
