const mysql = require('mysql2/promise');

async function debugHolidayUpdate() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('✅ Connected to database');
    
    // Get a sample holiday to test with (ID 1521 from the error)
    const [holiday] = await connection.execute('SELECT * FROM school_calendar WHERE calendar_id = 1521');
    
    if (holiday.length === 0) {
      console.log('❌ Holiday with ID 1521 not found');
      await connection.end();
      return;
    }
    
    const holidayData = holiday[0];
    console.log('🔍 Holiday data for ID 1521:');
    console.log(JSON.stringify(holidayData, null, 2));
    
    // Check what fields might be causing validation issues
    console.log('\n📋 Field analysis:');
    console.log('- title:', typeof holidayData.title, holidayData.title);
    console.log('- description:', typeof holidayData.description, holidayData.description);
    console.log('- event_date:', typeof holidayData.event_date, holidayData.event_date);
    console.log('- end_date:', typeof holidayData.end_date, holidayData.end_date);
    console.log('- category_id:', typeof holidayData.category_id, holidayData.category_id);
    console.log('- subcategory_id:', typeof holidayData.subcategory_id, holidayData.subcategory_id);
    console.log('- is_recurring:', typeof holidayData.is_recurring, holidayData.is_recurring);
    console.log('- recurrence_pattern:', typeof holidayData.recurrence_pattern, holidayData.recurrence_pattern);
    console.log('- is_active:', typeof holidayData.is_active, holidayData.is_active);
    console.log('- is_published:', typeof holidayData.is_published, holidayData.is_published);
    console.log('- allow_comments:', typeof holidayData.allow_comments, holidayData.allow_comments);
    console.log('- is_alert:', typeof holidayData.is_alert, holidayData.is_alert);
    
    // Check if this is a holiday
    console.log('\n🎯 Holiday-specific fields:');
    console.log('- is_holiday:', typeof holidayData.is_holiday, holidayData.is_holiday);
    console.log('- holiday_type:', typeof holidayData.holiday_type, holidayData.holiday_type);
    console.log('- country_code:', typeof holidayData.country_code, holidayData.country_code);
    console.log('- is_auto_generated:', typeof holidayData.is_auto_generated, holidayData.is_auto_generated);
    console.log('- api_source:', typeof holidayData.api_source, holidayData.api_source);
    
    // Check for any NULL or unusual values
    console.log('\n⚠️ Potential validation issues:');
    Object.keys(holidayData).forEach(key => {
      const value = holidayData[key];
      if (value === null) {
        console.log(`- ${key}: NULL`);
      } else if (value === '') {
        console.log(`- ${key}: empty string`);
      } else if (typeof value === 'string' && value.trim() === '') {
        console.log(`- ${key}: whitespace only`);
      }
    });
    
    await connection.end();
    console.log('\n✅ Database connection closed');
    console.log('\n💡 Check the frontend console for the exact data being sent in the PUT request');
    console.log('💡 The validation error might be due to:');
    console.log('   1. NULL subcategory_id being sent instead of omitting the field');
    console.log('   2. Boolean fields being sent as strings instead of actual booleans');
    console.log('   3. Date format issues');
    console.log('   4. Missing required fields in the update request');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugHolidayUpdate();
