const mysql = require('mysql2/promise');

async function clearHolidays() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'db_ebulletin_system'
    });
    
    console.log('Connected to database');
    
    // Delete existing auto-generated holidays for 2024
    const [result] = await connection.execute('DELETE FROM school_calendar WHERE YEAR(event_date) = 2024 AND is_auto_generated = 1');
    console.log(`Deleted ${result.affectedRows} auto-generated holidays for 2024`);
    
    await connection.end();
    console.log('Holidays cleared successfully!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

clearHolidays();
