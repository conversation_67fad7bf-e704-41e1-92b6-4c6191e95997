const holidayService = require('./src/services/holidayService');

async function simpleTest() {
  console.log('Simple Holiday Test...\n');

  try {
    // Test getting a few holidays
    console.log('=== Getting Philippine holidays ===');
    const philippineHolidays = holidayService.getPhilippineHolidays(2024);
    console.log(`Found ${philippineHolidays.length} Philippine holidays`);
    console.log('First 3 holidays:');
    philippineHolidays.slice(0, 3).forEach(holiday => {
      console.log(`- ${holiday.name} (${holiday.date})`);
    });

    // Test creating one holiday manually
    console.log('\n=== Testing single holiday creation ===');
    const testHoliday = philippineHolidays[0]; // New Year's Day
    console.log('Test holiday:', testHoliday);

    // Get category map
    const categoryMap = await holidayService.ensureHolidayCategories();
    console.log('Category map:', categoryMap);

    // Try to create the holiday
    console.log('\n=== Creating holiday in database ===');
    const result = await holidayService.createHolidayInDatabase(testHoliday, categoryMap, 1);
    console.log('Creation result:', result);

    // Check if it was created
    console.log('\n=== Checking if holiday exists ===');
    const existing = await holidayService.findExistingHoliday(testHoliday.date, testHoliday.name);
    console.log('Existing holiday:', existing);

  } catch (error) {
    console.error('Error in simple test:', error);
  }
}

simpleTest();
